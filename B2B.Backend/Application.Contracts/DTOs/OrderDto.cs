using Core.Enums;

namespace Application.Contracts.DTOs;

public class OrderDto
{
    public Guid Id { get; set; }
    public Guid CustomerId { get; set; }
    public Guid? AddressId { get; set; }
    public string OrderNumber { get; set; } = null!;
    public OrderStatus Status { get; set; }
    public decimal GrossTotalAmount { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal ShippingAmount { get; set; }
    public decimal TaxAmount { get; set; }
    public string? Notes { get; set; }
    public Guid? CouponId { get; set; }
    public string? CouponCode { get; set; }
    public decimal CouponDiscountAmount { get; set; }
    public int UsedPoints { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    // Navigation properties
    public CustomerDto? Customer { get; set; }
    public AddressDto? Address { get; set; }
    public List<OrderRowDto> OrderRows { get; set; } = [];
    public List<PaymentDto> Payments { get; set; } = [];
    public List<ShipmentDto> Shipments { get; set; } = [];
}

public class OrderListDto
{
    public Guid Id { get; set; }
    public string OrderNumber { get; set; } = null!;
    public OrderStatus Status { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal CouponDiscountAmount { get; set; }
    public int UsedPoints { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CustomerName { get; set; } = null!;
    public int ItemCount { get; set; }
}

public class OrderCreateDto
{
    public Guid CustomerId { get; set; }
    public Guid? AddressId { get; set; }
    public decimal GrossTotalAmount { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal DiscountAmount { get; set; } = 0;
    public decimal ShippingAmount { get; set; } = 0;
    public decimal TaxAmount { get; set; } = 0;
    public string? Notes { get; set; }
    public Guid? CouponId { get; set; }
    public string? CouponCode { get; set; }
    public decimal CouponDiscountAmount { get; set; } = 0;
    public int UsedPoints { get; set; } = 0;
    public List<OrderRowCreateDto> OrderRows { get; set; } = [];
}

public class OrderUpdateDto
{
    public Guid Id { get; set; }
    public OrderStatus Status { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal ShippingAmount { get; set; }
    public decimal TaxAmount { get; set; }
    public string? Notes { get; set; }
}

public class OrderStatusUpdateDto
{
    public Guid Id { get; set; }
    public OrderStatus Status { get; set; }
    public string? Notes { get; set; }
}

public class OrderRowDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public Guid ProductId { get; set; }
    public int Quantity { get; set; }
    public decimal Price { get; set; }
    public decimal DiscountedPrice { get; set; }
    public ProductDto? Product { get; set; }
}

public class OrderRowCreateDto
{
    public Guid ProductId { get; set; }
    public int Quantity { get; set; }
    public decimal Price { get; set; }
    public decimal DiscountedPrice { get; set; }
}

public class OrderTimelineDto
{
    public Guid OrderId { get; set; }
    public string OrderNumber { get; set; } = null!;
    public List<OrderTimelineEventDto> Events { get; set; } = [];
}

public class OrderTimelineEventDto
{
    public Guid Id { get; set; }
    public string EventType { get; set; } = null!; // "ORDER_CREATED", "PAYMENT_COMPLETED", "SHIPMENT_CREATED", "SHIPMENT_DELIVERED", etc.
    public string Title { get; set; } = null!;
    public string Description { get; set; } = null!;
    public DateTime EventDate { get; set; }
    public string? Status { get; set; }
    public string? Notes { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}
