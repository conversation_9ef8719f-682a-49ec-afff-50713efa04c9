using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Core.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Core.Entities
{
    [Table("Orders")]
    public partial class Order : BaseEntity
    {
        public Guid CustomerId { get; set; }
        public Guid? AddressId { get; set; }
        public string OrderNumber { get; set; } = null!;
        public OrderStatus Status { get; set; } = OrderStatus.Pending;

        /// <summary>
        /// Brüt toplam tutar (indirim öncesi)
        /// </summary>
        public decimal GrossTotalAmount { get; set; }

        /// <summary>
        /// Net toplam tutar (indirim sonrası)
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// İndirim tutarı
        /// </summary>
        public decimal DiscountAmount { get; set; } = 0;

        /// <summary>
        /// Kullanılan kupon ID'si (varsa)
        /// </summary>
        public Guid? CouponId { get; set; }

        /// <summary>
        /// Kullanılan kupon kodu (varsa)
        /// </summary>
        public string? CouponCode { get; set; }

        /// <summary>
        /// Kupon indirim tutarı (varsa)
        /// </summary>
        public decimal CouponDiscountAmount { get; set; } = 0;

        /// <summary>
        /// Kargo tutarı
        /// </summary>
        public decimal ShippingAmount { get; set; } = 0;

        /// <summary>
        /// Vergi tutarı
        /// </summary>
        public decimal TaxAmount { get; set; } = 0;
        public string? Notes { get; set; }
        public int UsedPoints { get; set; } = 0;

        //Foreign Key Mapping
        public Customer Customer { get; set; } = null!;
        public Address? Address { get; set; }
        public Coupon? Coupon { get; set; }
        public ICollection<OrderRow> OrderRows { get; set; } = [];
        public ICollection<Payment> Payments { get; set; } = [];
        public ICollection<Shipment> Shipments { get; set; } = [];
        public ICollection<OrderCampaign> OrderCampaigns { get; set; } = [];

        public static void Configure(EntityTypeBuilder<Order> builder)
        {
            builder.HasIndex(o => o.Id).IsUnique();
            builder.HasIndex(o => o.OrderNumber).IsUnique();
            builder.HasOne(o => o.Customer)
                .WithMany(u => u.Orders)
                .HasForeignKey(o => o.CustomerId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(o => o.Coupon)
                .WithMany()
                .HasForeignKey(o => o.CouponId)
                .OnDelete(DeleteBehavior.SetNull);

        }
    }
    [Table("OrdersHistory")]
    public class OrderHistory : HistoryBaseEntity
    {
        // Entity properties
        public Guid CustomerId { get; set; }
        public Guid? AddressId { get; set; }
        public string OrderNumber { get; set; } = null!;
        public OrderStatus Status { get; set; } = OrderStatus.Pending;
        public decimal GrossTotalAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal DiscountAmount { get; set; } = 0;
        public decimal ShippingAmount { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public string? Notes { get; set; }
        public Guid? CouponId { get; set; }
        public string? CouponCode { get; set; }
        public decimal CouponDiscountAmount { get; set; } = 0;
    }
}