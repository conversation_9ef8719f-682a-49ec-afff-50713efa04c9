namespace Core.Events;

/// <summary>
/// Sipariş onay maili event'i
/// Sipariş başarıyla oluşturulduktan sonra fırlatılır
/// </summary>
public class OrderConfirmationNotificationRequested
{
    /// <summary>
    /// Sipariş ID
    /// </summary>
    public Guid OrderId { get; set; }

    /// <summary>
    /// Müşteri e-posta adresi
    /// </summary>
    public string CustomerEmail { get; set; } = null!;

    /// <summary>
    /// Müşteri adı soyadı
    /// </summary>
    public string CustomerName { get; set; } = null!;

    /// <summary>
    /// Müşteri telefonu
    /// </summary>
    public string CustomerPhone { get; set; } = null!;

    /// <summary>
    /// Sipariş numarası
    /// </summary>
    public string OrderNumber { get; set; } = null!;

    /// <summary>
    /// Sipariş tarihi
    /// </summary>
    public DateTime OrderDate { get; set; }

    /// <summary>
    /// Sipariş toplam tutarı (KDV dahil)
    /// </summary>
    public decimal OrderTotalAmount { get; set; }

    /// <summary>
    /// Sipariş ara toplam (KDV hariç)
    /// </summary>
    public decimal OrderSubtotal { get; set; }

    /// <summary>
    /// İndirim tutarı
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// Kargo tutarı
    /// </summary>
    public decimal ShippingAmount { get; set; }

    /// <summary>
    /// KDV tutarı
    /// </summary>
    public decimal TaxAmount { get; set; }

    /// <summary>
    /// Kullanılan puan miktarı
    /// </summary>
    public int UsedPoints { get; set; }

    /// <summary>
    /// Kullanılan kupon kodu (varsa)
    /// </summary>
    public string? CouponCode { get; set; }
    /// <summary>
    /// Teslimat adresi
    /// </summary>
    public string DeliveryAddress { get; set; } = null!;

    /// <summary>
    /// Teslimat şehri
    /// </summary>
    public string DeliveryCity { get; set; } = null!;

    /// <summary>
    /// Teslimat ilçesi
    /// </summary>
    public string DeliveryCounty { get; set; } = null!;

    /// <summary>
    /// Teslimat ülkesi
    /// </summary>
    public string DeliveryCountry { get; set; } = null!;

    /// <summary>
    /// Fatura adresi
    /// </summary>
    public string BillingAddress { get; set; } = null!;

    /// <summary>
    /// Fatura şehri
    /// </summary>
    public string BillingCity { get; set; } = null!;

    /// <summary>
    /// Fatura ilçesi
    /// </summary>
    public string BillingCounty { get; set; } = null!;

    /// <summary>
    /// Fatura ülkesi
    /// </summary>
    public string BillingCountry { get; set; } = null!;

    /// <summary>
    /// Sipariş detay linki
    /// </summary>
    public string OrderDetailUrl { get; set; } = null!;

    /// <summary>
    /// Sipariş ürünleri
    /// </summary>
    public List<OrderItemForMail> OrderItems { get; set; } = [];

    /// <summary>
    /// Event oluşturulma zamanı
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Mail için sipariş ürün bilgisi
/// </summary>
public class OrderItemForMail
{
    /// <summary>
    /// Ürün adı
    /// </summary>
    public string ProductName { get; set; } = null!;

    /// <summary>
    /// Ürün görseli URL
    /// </summary>
    public string ProductImageUrl { get; set; } = null!;

    /// <summary>
    /// Ürün miktarı
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// Ürün birim fiyatı
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// İndirimli birim fiyat
    /// </summary>
    public decimal DiscountedPrice { get; set; }

    /// <summary>
    /// Varyant bilgisi (renk, beden vs.)
    /// </summary>
    public string? VariantInfo { get; set; }
}
