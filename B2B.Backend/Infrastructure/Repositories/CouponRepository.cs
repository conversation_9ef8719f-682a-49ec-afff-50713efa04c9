using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repositories;

public class CouponRepository : GenericRepository<Coupon>, ICouponRepository
{
    private readonly B2BDbContext _context;

    public CouponRepository(B2BDbContext context) : base(context)
    {
        _context = context;
    }

    public async Task<Coupon?> GetByCouponCodeAsync(string couponCode)
    {
        return await _context.Coupons
            .Include(c => c.Customer)
            .Include(c => c.CouponUsages)
            .FirstOrDefaultAsync(c => c.CouponCode == couponCode && !c.IsDeleted);
    }

    public async Task<List<Coupon>> GetCustomerCouponsAsync(Guid customerId)
    {
        // Müşteriye özel kuponlar + müşterinin kullandığı genel kuponlar
        var customerSpecificCoupons = await _context.Coupons
            .Include(c => c.Customer)
            .Include(c => c.CouponUsages)
            .Where(c => c.CustomerId == customerId && !c.IsDeleted)
            .ToListAsync();

        var usedGeneralCoupons = await _context.CouponUsages
            .Include(cu => cu.Coupon)
            .ThenInclude(c => c.Customer)
            .Where(cu => cu.CustomerId == customerId && cu.Coupon.CouponType == CouponType.General)
            .Select(cu => cu.Coupon)
            .Distinct()
            .ToListAsync();

        var allCoupons = customerSpecificCoupons.Concat(usedGeneralCoupons).Distinct().ToList();
        return allCoupons.OrderByDescending(c => c.CreatedAt).ToList();
    }

    public async Task<List<Coupon>> GetCustomerCouponsPagedAsync(Guid customerId, int page, int pageSize)
    {
        var coupons = await GetCustomerCouponsAsync(customerId);
        return coupons
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToList();
    }

    public async Task<List<Coupon>> GetCustomerActiveCouponsAsync(Guid customerId)
    {
        var now = DateTime.UtcNow;

        // Müşteriye özel aktif kuponlar
        var customerSpecificCoupons = await _context.Coupons
            .Include(c => c.Customer)
            .Include(c => c.CouponUsages)
            .Where(c => c.CustomerId == customerId && !c.IsDeleted &&
                       c.IsActive && c.ExpirationDate > now)
            .ToListAsync();

        // Genel kuponlar (henüz kullanılmamış veya kullanım limiti dolmamış)
        var generalCoupons = await _context.Coupons
            .Include(c => c.CouponUsages)
            .Where(c => c.CouponType == CouponType.General && !c.IsDeleted &&
                       c.IsActive && c.ExpirationDate > now)
            .ToListAsync();

        // Genel kuponları filtrele (müşteri kullanım limiti kontrolü)
        var availableGeneralCoupons = new List<Coupon>();
        foreach (var coupon in generalCoupons)
        {
            var customerUsageCount = coupon.CouponUsages.Count(cu => cu.CustomerId == customerId);
            if (customerUsageCount < coupon.UsageLimitPerCustomer &&
                (coupon.TotalUsageLimit == null || coupon.TotalUsageCount < coupon.TotalUsageLimit))
            {
                availableGeneralCoupons.Add(coupon);
            }
        }
        foreach (var coupon in customerSpecificCoupons)
        {
            availableGeneralCoupons.Add(coupon);
        }
        var allActiveCoupons = customerSpecificCoupons.Concat(availableGeneralCoupons).ToList();
        return allActiveCoupons.OrderByDescending(c => c.CreatedAt).ToList();
    }

    public async Task<List<Coupon>> GetCustomerUsedCouponsAsync(Guid customerId)
    {
        // Müşterinin kullandığı kuponları getir
        var usedCoupons = await _context.CouponUsages
            .Include(cu => cu.Coupon)
            .ThenInclude(c => c.Customer)
            .Where(cu => cu.CustomerId == customerId)
            .Select(cu => cu.Coupon)
            .Distinct()
            .OrderByDescending(c => c.UpdatedAt)
            .ToListAsync();

        return usedCoupons;
    }

    public async Task<List<Coupon>> GetCustomerExpiredCouponsAsync(Guid customerId)
    {
        var now = DateTime.UtcNow;

        // Müşteriye özel süresi dolmuş kuponlar
        var expiredCustomerCoupons = await _context.Coupons
            .Include(c => c.Customer)
            .Where(c => c.CustomerId == customerId && !c.IsDeleted &&
                       c.ExpirationDate <= now)
            .ToListAsync();

        // Müşterinin daha önce kullandığı ama artık süresi dolmuş genel kuponlar
        var expiredGeneralCoupons = await _context.CouponUsages
            .Include(cu => cu.Coupon)
            .Where(cu => cu.CustomerId == customerId &&
                        cu.Coupon.CouponType == CouponType.General &&
                        cu.Coupon.ExpirationDate <= now)
            .Select(cu => cu.Coupon)
            .Distinct()
            .ToListAsync();

        var allExpiredCoupons = expiredCustomerCoupons.Concat(expiredGeneralCoupons).Distinct().ToList();
        return allExpiredCoupons.OrderByDescending(c => c.ExpirationDate).ToList();
    }

    public async Task<List<Coupon>> GetActiveCouponsAsync()
    {
        var now = DateTime.UtcNow;
        return await _context.Coupons
            .Include(c => c.Customer)
            .Include(c => c.CouponUsages)
            .Where(c => !c.IsDeleted && c.IsActive && c.ExpirationDate > now)
            .OrderByDescending(c => c.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<Coupon>> GetExpiredCouponsAsync()
    {
        var now = DateTime.UtcNow;
        return await _context.Coupons
            .Include(c => c.Customer)
            .Include(c => c.CouponUsages)
            .Where(c => !c.IsDeleted && c.ExpirationDate <= now)
            .OrderByDescending(c => c.ExpirationDate)
            .ToListAsync();
    }

    public async Task<List<Coupon>> GetUsedCouponsAsync()
    {
        // Kullanılmış kuponları getir (kullanım geçmişi olan)
        var usedCouponIds = await _context.CouponUsages
            .Select(cu => cu.CouponId)
            .Distinct()
            .ToListAsync();

        return await _context.Coupons
            .Include(c => c.Customer)
            .Include(c => c.CouponUsages)
            .Where(c => !c.IsDeleted && usedCouponIds.Contains(c.Id))
            .OrderByDescending(c => c.UpdatedAt)
            .ToListAsync();
    }

    public async Task<List<Coupon>> GetCouponsByStatusAsync(bool isUsed, bool isExpired)
    {
        var now = DateTime.UtcNow;
        var query = _context.Coupons
            .Include(c => c.Customer)
            .Include(c => c.CouponUsages)
            .Where(c => !c.IsDeleted);

        if (isUsed)
        {
            // Kullanılmış kuponlar (kullanım geçmişi olan)
            var usedCouponIds = await _context.CouponUsages
                .Select(cu => cu.CouponId)
                .Distinct()
                .ToListAsync();
            query = query.Where(c => usedCouponIds.Contains(c.Id));
        }

        if (isExpired)
        {
            query = query.Where(c => c.ExpirationDate <= now);
        }

        return await query.OrderByDescending(c => c.CreatedAt).ToListAsync();
    }

    public async Task<bool> IsCouponCodeUniqueAsync(string couponCode, Guid? excludeId = null)
    {
        var query = _context.Coupons.Where(c => c.CouponCode == couponCode && !c.IsDeleted);

        if (excludeId.HasValue)
        {
            query = query.Where(c => c.Id != excludeId.Value);
        }

        return !await query.AnyAsync();
    }

    public async Task<bool> IsValidCouponAsync(string couponCode)
    {
        var now = DateTime.UtcNow;
        return await _context.Coupons
            .AnyAsync(c => c.CouponCode == couponCode && !c.IsDeleted &&
                          c.IsActive && c.ExpirationDate > now);
    }

    public async Task<bool> IsCouponExpiredAsync(Guid couponId)
    {
        var now = DateTime.UtcNow;
        var coupon = await _context.Coupons.FindAsync(couponId);
        return coupon != null && coupon.ExpirationDate <= now;
    }

    public async Task<bool> CanUseCouponAsync(Guid couponId, Guid customerId)
    {
        var now = DateTime.UtcNow;
        var coupon = await _context.Coupons
            .Include(c => c.CouponUsages)
            .FirstOrDefaultAsync(c => c.Id == couponId);

        if (coupon == null || coupon.IsDeleted || !coupon.IsActive || coupon.ExpirationDate <= now)
            return false;

        // Müşteri özel kupon kontrolü
        if (coupon.CouponType == CouponType.CustomerSpecific)
        {
            return coupon.CustomerId == customerId;
        }

        // Genel kupon kontrolü
        if (coupon.CouponType == CouponType.General)
        {
            // Toplam kullanım limiti kontrolü
            if (coupon.TotalUsageLimit.HasValue && coupon.TotalUsageCount >= coupon.TotalUsageLimit.Value)
                return false;

            // Müşteri başına kullanım limiti kontrolü
            var customerUsageCount = coupon.CouponUsages.Count(cu => cu.CustomerId == customerId);
            return customerUsageCount < coupon.UsageLimitPerCustomer;
        }

        return false;
    }

    public async Task<bool> UseCouponAsync(Guid couponId)
    {
        // Bu metod artık kullanılmıyor, CouponUsage tablosu üzerinden yönetiliyor
        // Geriye dönük uyumluluk için bırakıldı
        return false;
    }

    public async Task<bool> ResetUsageAsync(Guid couponId)
    {
        // Kuponun tüm kullanım geçmişini sil
        var usages = await _context.CouponUsages
            .Where(cu => cu.CouponId == couponId)
            .ToListAsync();

        if (usages.Any())
        {
            _context.CouponUsages.RemoveRange(usages);

            // Kuponun toplam kullanım sayısını güncelle
            var coupon = await _context.Coupons.FindAsync(couponId);
            if (coupon != null)
            {
                coupon.TotalUsageCount = 0;
                coupon.UpdatedAt = DateTime.UtcNow;
                _context.Coupons.Update(coupon);
            }

            return await _context.SaveChangesAsync() > 0;
        }

        return true;
    }

    public async Task<int> GetCouponUsageCountAsync(Guid couponId)
    {
        return await _context.CouponUsages
            .CountAsync(cu => cu.CouponId == couponId);
    }

    public async Task<List<Coupon>> SearchCouponsAsync(string searchTerm)
    {
        return await _context.Coupons
            .Include(c => c.Customer)
            .Include(c => c.CouponUsages)
            .Where(c => !c.IsDeleted &&
                       (c.CouponCode.Contains(searchTerm) ||
                        (c.Name != null && c.Name.Contains(searchTerm)) ||
                        (c.Customer != null && c.Customer.NameSurname.Contains(searchTerm))))
            .OrderByDescending(c => c.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<Coupon>> GetCouponsByDiscountTypeAsync(DiscountType discountType)
    {
        return await _context.Coupons
            .Include(c => c.Customer)
            .Include(c => c.CouponUsages)
            .Where(c => !c.IsDeleted && c.DiscountType == discountType)
            .OrderByDescending(c => c.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<Coupon>> GetCouponsByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        return await _context.Coupons
            .Include(c => c.Customer)
            .Include(c => c.CouponUsages)
            .Where(c => !c.IsDeleted && c.CreatedAt >= startDate && c.CreatedAt <= endDate)
            .OrderByDescending(c => c.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<Coupon>> GetCouponsByAmountRangeAsync(decimal minAmount, decimal maxAmount)
    {
        return await _context.Coupons
            .Include(c => c.Customer)
            .Include(c => c.CouponUsages)
            .Where(c => !c.IsDeleted && c.DiscountAmount >= minAmount && c.DiscountAmount <= maxAmount)
            .OrderByDescending(c => c.CreatedAt)
            .ToListAsync();
    }

    public async Task<int> GetTotalCouponsAsync()
    {
        return await _context.Coupons.CountAsync(c => !c.IsDeleted);
    }

    public async Task<int> GetActiveCouponsCountAsync()
    {
        var now = DateTime.UtcNow;
        return await _context.Coupons
            .CountAsync(c => !c.IsDeleted && c.IsActive && c.ExpirationDate > now);
    }

    public async Task<int> GetUsedCouponsCountAsync()
    {
        var usedCouponIds = await _context.CouponUsages
            .Select(cu => cu.CouponId)
            .Distinct()
            .ToListAsync();

        return await _context.Coupons
            .CountAsync(c => !c.IsDeleted && usedCouponIds.Contains(c.Id));
    }

    public async Task<int> GetExpiredCouponsCountAsync()
    {
        var now = DateTime.UtcNow;
        return await _context.Coupons
            .CountAsync(c => !c.IsDeleted && c.ExpirationDate <= now);
    }

    public async Task<decimal> GetTotalDiscountGivenAsync()
    {
        return await _context.CouponUsages
            .SumAsync(cu => cu.DiscountApplied);
    }

    public async Task<decimal> GetAverageDiscountAmountAsync()
    {
        var coupons = await _context.Coupons
            .Where(c => !c.IsDeleted)
            .Select(c => c.DiscountAmount)
            .ToListAsync();

        return coupons.Any() ? coupons.Average() : 0;
    }

    public async Task<List<Coupon>> GetTopCustomersByCouponUsageAsync(int count = 10)
    {
        var topCustomers = await _context.CouponUsages
            .GroupBy(cu => cu.CustomerId)
            .OrderByDescending(g => g.Count())
            .Take(count)
            .Select(g => g.Key)
            .ToListAsync();

        return await _context.Coupons
            .Include(c => c.Customer)
            .Include(c => c.CouponUsages)
            .Where(c => !c.IsDeleted && topCustomers.Contains(c.CustomerId ?? Guid.Empty))
            .ToListAsync();
    }

    public async Task<bool> BulkDeleteAsync(List<Guid> couponIds)
    {
        var coupons = await _context.Coupons
            .Where(c => couponIds.Contains(c.Id) && !c.IsDeleted)
            .ToListAsync();

        foreach (var coupon in coupons)
        {
            coupon.IsDeleted = true;
            coupon.UpdatedAt = DateTime.UtcNow;
        }

        _context.Coupons.UpdateRange(coupons);
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<bool> BulkUpdateExpirationAsync(List<Guid> couponIds, DateTime newExpirationDate)
    {
        var coupons = await _context.Coupons
            .Where(c => couponIds.Contains(c.Id) && !c.IsDeleted)
            .ToListAsync();

        foreach (var coupon in coupons)
        {
            coupon.ExpirationDate = newExpirationDate;
            coupon.UpdatedAt = DateTime.UtcNow;
        }

        _context.Coupons.UpdateRange(coupons);
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task AddToHistoryAsync(Coupon coupon, ChangeType changeType, Guid employeeId)
    {
        var history = new CouponHistory
        {
            Id = Guid.CreateVersion7(),
            EntityId = coupon.Id,
            ChangeType = changeType,
            ChangeDate = DateTime.UtcNow,
            EmployeeId = employeeId,
            CreatedAt = coupon.CreatedAt,
            UpdatedAt = coupon.UpdatedAt,
            IsDeleted = coupon.IsDeleted,
            IsActive = coupon.IsActive,

            // Copy entity properties
            CouponCode = coupon.CouponCode,
            Name = coupon.Name,
            Description = coupon.Description,
            DiscountType = coupon.DiscountType,
            DiscountAmount = coupon.DiscountAmount,
            ExpirationDate = coupon.ExpirationDate,
            CouponType = coupon.CouponType,
            CustomerId = coupon.CustomerId,
            TotalUsageLimit = coupon.TotalUsageLimit,
            UsageLimitPerCustomer = coupon.UsageLimitPerCustomer,
            TotalUsageCount = coupon.TotalUsageCount,
            MinimumCartAmount = coupon.MinimumCartAmount
        };

        await _context.CouponHistory.AddAsync(history);
    }

    // Coupon usage management methods
    public async Task<bool> CreateCouponUsageAsync(CouponUsage couponUsage)
    {
        await _context.CouponUsages.AddAsync(couponUsage);

        // Update coupon total usage count
        var coupon = await _context.Coupons.FindAsync(couponUsage.CouponId);
        if (coupon != null)
        {
            coupon.TotalUsageCount++;
            coupon.UpdatedAt = DateTime.UtcNow;
            _context.Coupons.Update(coupon);
        }

        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<List<CouponUsage>> GetCouponUsageHistoryAsync(Guid couponId)
    {
        return await _context.CouponUsages
            .Include(cu => cu.Customer)
            .Include(cu => cu.Order)
            .Where(cu => cu.CouponId == couponId)
            .OrderByDescending(cu => cu.UsageDate)
            .ToListAsync();
    }

    public async Task<List<CouponUsage>> GetCustomerCouponUsageHistoryAsync(Guid customerId)
    {
        return await _context.CouponUsages
            .Include(cu => cu.Coupon)
            .Include(cu => cu.Order)
            .Where(cu => cu.CustomerId == customerId)
            .OrderByDescending(cu => cu.UsageDate)
            .ToListAsync();
    }

    public async Task<int> GetCustomerCouponUsageCountAsync(Guid couponId, Guid customerId)
    {
        return await _context.CouponUsages
            .CountAsync(cu => cu.CouponId == couponId && cu.CustomerId == customerId);
    }
}
