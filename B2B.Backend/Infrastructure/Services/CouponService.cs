using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

public class CouponService : ICouponService
{
    private readonly ICouponRepository _couponRepository;
    private readonly IEncryptionService _encryptionService;

    public CouponService(
        ICouponRepository couponRepository,
        IEncryptionService encryptionService)
    {
        _couponRepository = couponRepository;
        _encryptionService = encryptionService;
    }

    public async Task<List<CouponListDto>> GetListAsync(int? page = null, int? pageSize = null)
    {
        var coupons = await _couponRepository.GetPagedAsync(page, pageSize);
        var result = new List<CouponListDto>();

        foreach (var coupon in coupons.Where(c => !c.IsDeleted))
        {
            result.Add(MapToCouponListDto(coupon));
        }

        return result.OrderByDescending(c => c.CreatedAt).ToList();
    }

    public async Task<CouponDto?> GetByIdAsync(Guid id)
    {
        var coupon = await _couponRepository.GetByIdAsync(id);
        if (coupon == null || coupon.IsDeleted)
            return null;

        return MapToCouponDto(coupon);
    }

    public async Task<CouponDto?> GetByCouponCodeAsync(string couponCode)
    {
        var coupon = await _couponRepository.GetByCouponCodeAsync(couponCode);
        if (coupon == null || coupon.IsDeleted)
            return null;

        return MapToCouponDto(coupon);
    }

    public async Task<Guid> CreateAsync(CouponCreateDto dto)
    {
        // Check if coupon code is unique
        if (!await _couponRepository.IsCouponCodeUniqueAsync(dto.CouponCode))
            throw new InvalidOperationException($"Kupon kodu '{dto.CouponCode}' zaten kullanımda.");

        // Validate expiration date
        if (dto.ExpirationDate <= DateTime.UtcNow)
            throw new InvalidOperationException("Son kullanma tarihi gelecekte olmalıdır.");

        var coupon = new Coupon
        {
            Id = Guid.CreateVersion7(),
            CouponCode = dto.CouponCode,
            Name = dto.Name,
            Description = dto.Description,
            DiscountType = dto.DiscountType,
            DiscountAmount = dto.DiscountAmount,
            ExpirationDate = dto.ExpirationDate,
            CouponType = dto.CouponType,
            CustomerId = dto.CouponType == CouponType.CustomerSpecific ? dto.CustomerId : null,
            TotalUsageLimit = dto.TotalUsageLimit,
            UsageLimitPerCustomer = dto.UsageLimitPerCustomer,
            TotalUsageCount = 0,
            MinimumCartAmount = dto.MinimumCartAmount,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _couponRepository.AddAsync(coupon);
        await _couponRepository.SaveChangesAsync();

        // Add to history
        await _couponRepository.AddToHistoryAsync(coupon, ChangeType.Created, Guid.Empty);
        await _couponRepository.SaveChangesAsync();

        return coupon.Id;
    }

    public async Task UpdateAsync(CouponUpdateDto dto)
    {
        var coupon = await _couponRepository.GetByIdAsync(dto.Id);
        if (coupon == null || coupon.IsDeleted)
            throw new ArgumentException("Kupon bulunamadı");

        // Check if coupon code is unique (excluding current coupon)
        if (!await _couponRepository.IsCouponCodeUniqueAsync(dto.CouponCode, dto.Id))
            throw new InvalidOperationException($"Kupon kodu '{dto.CouponCode}' zaten kullanımda.");

        // Validate expiration date
        if (dto.ExpirationDate <= DateTime.UtcNow)
            throw new InvalidOperationException("Son kullanma tarihi gelecekte olmalıdır.");

        coupon.CouponCode = dto.CouponCode;
        coupon.Name = dto.Name;
        coupon.Description = dto.Description;
        coupon.DiscountType = dto.DiscountType;
        coupon.DiscountAmount = dto.DiscountAmount;
        coupon.ExpirationDate = dto.ExpirationDate;
        coupon.CouponType = dto.CouponType;
        coupon.CustomerId = dto.CouponType == CouponType.CustomerSpecific ? dto.CustomerId : null;
        coupon.TotalUsageLimit = dto.TotalUsageLimit;
        coupon.UsageLimitPerCustomer = dto.UsageLimitPerCustomer;
        coupon.MinimumCartAmount = dto.MinimumCartAmount;
        coupon.IsActive = dto.IsActive;
        coupon.UpdatedAt = DateTime.UtcNow;

        _couponRepository.Update(coupon);
        await _couponRepository.SaveChangesAsync();

        // Add to history
        await _couponRepository.AddToHistoryAsync(coupon, ChangeType.Updated, Guid.Empty);
        await _couponRepository.SaveChangesAsync();
    }

    public async Task DeleteAsync(Guid id)
    {
        var coupon = await _couponRepository.GetByIdAsync(id);
        if (coupon == null || coupon.IsDeleted)
            throw new ArgumentException("Kupon bulunamadı");

        coupon.IsDeleted = true;
        coupon.UpdatedAt = DateTime.UtcNow;

        _couponRepository.Update(coupon);
        await _couponRepository.SaveChangesAsync();

        // Add to history
        await _couponRepository.AddToHistoryAsync(coupon, ChangeType.Deleted, Guid.Empty);
        await _couponRepository.SaveChangesAsync();
    }

    public async Task<CouponValidationDto> ValidateCouponAsync(string couponCode, Guid customerId)
    {
        var coupon = await _couponRepository.GetByCouponCodeAsync(couponCode);
        
        if (coupon == null || coupon.IsDeleted)
        {
            return new CouponValidationDto
            {
                IsValid = false,
                Message = "Kupon bulunamadı.",
                Coupon = null,
                DiscountAmount = 0,
                DiscountType = DiscountType.Fixed
            };
        }

        // Bu kontrol gereksiz - müşteri özel kupon kontrolü aşağıda yapılıyor
        // Genel kuponlar için CustomerId null olabilir

        if (coupon.ExpirationDate <= DateTime.UtcNow)
        {
            return new CouponValidationDto
            {
                IsValid = false,
                Message = "Kuponun süresi dolmuş.",
                Coupon = MapToCouponDto(coupon),
                DiscountAmount = 0,
                DiscountType = coupon.DiscountType
            };
        }

        // Kupon aktif değilse
        if (!coupon.IsActive)
        {
            return new CouponValidationDto
            {
                IsValid = false,
                Message = "Kupon aktif değil.",
                Coupon = MapToCouponDto(coupon),
                DiscountAmount = 0,
                DiscountType = coupon.DiscountType
            };
        }

        // Toplam kullanım limitini kontrol et
        if (coupon.TotalUsageLimit.HasValue && coupon.TotalUsageCount >= coupon.TotalUsageLimit.Value)
        {
            return new CouponValidationDto
            {
                IsValid = false,
                Message = "Kupon toplam kullanım limitine ulaşmış.",
                Coupon = MapToCouponDto(coupon),
                DiscountAmount = 0,
                DiscountType = coupon.DiscountType
            };
        }

        // Müşteri özel kupon kontrolü
        if (coupon.CouponType == CouponType.CustomerSpecific && coupon.CustomerId != customerId)
        {
            return new CouponValidationDto
            {
                IsValid = false,
                Message = "Bu kupon size özel değil.",
                Coupon = MapToCouponDto(coupon),
                DiscountAmount = 0,
                DiscountType = coupon.DiscountType
            };
        }

        // Müşteri başına kullanım limitini kontrol et
        var customerUsageCount = await _couponRepository.GetCustomerCouponUsageCountAsync(coupon.Id, customerId);
        if (customerUsageCount >= coupon.UsageLimitPerCustomer)
        {
            return new CouponValidationDto
            {
                IsValid = false,
                Message = "Bu kuponu kullanım limitinize ulaştınız.",
                Coupon = MapToCouponDto(coupon),
                DiscountAmount = 0,
                DiscountType = coupon.DiscountType
            };
        }

        return new CouponValidationDto
        {
            IsValid = true,
            Message = "Kupon geçerli.",
            Coupon = MapToCouponDto(coupon),
            DiscountAmount = coupon.DiscountAmount,
            DiscountType = coupon.DiscountType
        };
    }

    public async Task<bool> UseCouponAsync(CouponUsageDto dto)
    {
        var validation = await ValidateCouponAsync(dto.CouponCode, dto.CustomerId);
        if (!validation.IsValid)
            throw new InvalidOperationException(validation.Message);

        var coupon = await _couponRepository.GetByIdAsync(dto.CouponId);
        if (coupon == null)
            throw new InvalidOperationException("Kupon bulunamadı.");

        // Kupon kullanımını kaydet
        var couponUsage = new CouponUsage
        {
            Id = Guid.CreateVersion7(),
            CouponId = dto.CouponId,
            CustomerId = dto.CustomerId,
            OrderId = dto.OrderId,
            DiscountApplied = dto.DiscountApplied,
            OrderAmount = dto.OrderAmount,
            UsageDate = DateTime.UtcNow,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var success = await _couponRepository.CreateCouponUsageAsync(couponUsage);

        if (success)
        {
            // Kuponun toplam kullanım sayısını artır
            coupon.TotalUsageCount++;
            coupon.UpdatedAt = DateTime.UtcNow;

            _couponRepository.Update(coupon);
            await _couponRepository.SaveChangesAsync();

            // History'ye ekle
            await _couponRepository.AddToHistoryAsync(coupon, ChangeType.Updated, Guid.Empty);
            await _couponRepository.SaveChangesAsync();
        }

        return success;
    }

    public async Task<bool> IsValidCouponAsync(string couponCode)
    {
        return await _couponRepository.IsValidCouponAsync(couponCode);
    }

    public async Task<bool> IsCouponExpiredAsync(Guid couponId)
    {
        return await _couponRepository.IsCouponExpiredAsync(couponId);
    }

    public async Task<List<CouponListDto>> GetCustomerCouponsAsync(Guid customerId, int? page = null, int? pageSize = null)
    {
        List<Coupon> coupons;
        
        if (page.HasValue && pageSize.HasValue)
        {
            coupons = await _couponRepository.GetCustomerCouponsPagedAsync(customerId, page.Value, pageSize.Value);
        }
        else
        {
            coupons = await _couponRepository.GetCustomerCouponsAsync(customerId);
        }

        return coupons.Select(MapToCouponListDto).ToList();
    }

    public async Task<CouponSummaryDto?> GetCustomerSummaryAsync(Guid customerId)
    {
        var allCoupons = await _couponRepository.GetCustomerCouponsAsync(customerId);
        var now = DateTime.UtcNow;

        var activeCoupons = allCoupons.Where(c => c.IsActive && c.ExpirationDate > now && (c.TotalUsageLimit == null || c.TotalUsageCount < c.TotalUsageLimit)).Count();
        var usedCoupons = allCoupons.Where(c => c.TotalUsageLimit.HasValue && c.TotalUsageCount >= c.TotalUsageLimit.Value).Count();
        var expiredCoupons = allCoupons.Where(c => c.ExpirationDate <= now).Count();

        var recentCoupons = allCoupons.Take(5).Select(MapToCouponListDto).ToList();

        return new CouponSummaryDto
        {
            CustomerId = customerId,
            TotalCoupons = allCoupons.Count,
            ActiveCoupons = activeCoupons,
            UsedCoupons = usedCoupons,
            ExpiredCoupons = expiredCoupons,
            RecentCoupons = recentCoupons
        };
    }

    public async Task<List<CouponListDto>> GetCustomerActiveCouponsAsync(Guid customerId)
    {
        var coupons = await _couponRepository.GetCustomerActiveCouponsAsync(customerId);
        return coupons.Select(MapToCouponListDto).ToList();
    }

    public async Task<List<CouponListDto>> GetCustomerUsedCouponsAsync(Guid customerId)
    {
        var coupons = await _couponRepository.GetCustomerUsedCouponsAsync(customerId);
        return coupons.Select(MapToCouponListDto).ToList();
    }

    public async Task<List<CouponListDto>> GetCustomerExpiredCouponsAsync(Guid customerId)
    {
        var coupons = await _couponRepository.GetCustomerExpiredCouponsAsync(customerId);
        return coupons.Select(MapToCouponListDto).ToList();
    }

    private CouponDto MapToCouponDto(Coupon coupon)
    {
        var now = DateTime.UtcNow;
        var isExpired = coupon.ExpirationDate <= now;
        var totalUsageLimit = coupon.TotalUsageLimit ?? int.MaxValue;
        var isUsedUp = coupon.TotalUsageCount >= totalUsageLimit;

        return new CouponDto
        {
            Id = coupon.Id,
            CouponCode = coupon.CouponCode,
            Name = coupon.Name,
            Description = coupon.Description,
            DiscountType = coupon.DiscountType,
            DiscountTypeText = coupon.DiscountType == DiscountType.Fixed ? "Sabit Tutar" : "Yüzde",
            DiscountAmount = coupon.DiscountAmount,
            ExpirationDate = coupon.ExpirationDate,
            CouponType = coupon.CouponType,
            CouponTypeText = coupon.CouponType == CouponType.CustomerSpecific ? "Müşteri Özel" : "Genel",
            CustomerId = coupon.CustomerId,
            CustomerName = coupon.Customer != null ? _encryptionService.DecryptIfNotEmpty(coupon.Customer.NameSurname) : null,
            TotalUsageLimit = coupon.TotalUsageLimit,
            UsageLimitPerCustomer = coupon.UsageLimitPerCustomer,
            TotalUsageCount = coupon.TotalUsageCount,
            MinimumCartAmount = coupon.MinimumCartAmount,
            IsExpired = isExpired,
            IsActive = coupon.IsActive && !isExpired && !isUsedUp,
            RemainingUsage = coupon.TotalUsageLimit.HasValue ? Math.Max(0, coupon.TotalUsageLimit.Value - coupon.TotalUsageCount) : int.MaxValue,
            CreatedAt = coupon.CreatedAt,
            UpdatedAt = coupon.UpdatedAt
        };
    }

    private CouponListDto MapToCouponListDto(Coupon coupon)
    {
        var now = DateTime.UtcNow;
        var isExpired = coupon.ExpirationDate <= now;
        var totalUsageLimit = coupon.TotalUsageLimit ?? int.MaxValue;
        var isUsedUp = coupon.TotalUsageCount >= totalUsageLimit;

        string status;
        if (isExpired)
            status = "Süresi Doldu";
        else if (isUsedUp)
            status = "Kullanıldı";
        else if (!coupon.IsActive)
            status = "Pasif";
        else
            status = "Aktif";

        return new CouponListDto
        {
            Id = coupon.Id,
            CouponCode = coupon.CouponCode,
            Name = coupon.Name,
            CouponType = coupon.CouponType,
            CouponTypeText = coupon.CouponType == CouponType.CustomerSpecific ? "Müşteri Özel" : "Genel",
            CustomerId = coupon.CustomerId,
            CustomerName = coupon.Customer != null ? _encryptionService.DecryptIfNotEmpty(coupon.Customer.NameSurname) : null,
            DiscountTypeText = coupon.DiscountType == DiscountType.Fixed ? "Sabit Tutar" : "Yüzde",
            DiscountAmount = coupon.DiscountAmount,
            ExpirationDate = coupon.ExpirationDate,
            Status = status,
            TotalUsageCount = coupon.TotalUsageCount,
            TotalUsageLimit = coupon.TotalUsageLimit,
            UsageLimitPerCustomer = coupon.UsageLimitPerCustomer,
            MinimumCartAmount = coupon.MinimumCartAmount,
            Description = coupon.Description,
            IsActive = coupon.IsActive,
            CreatedAt = coupon.CreatedAt
        };
    }

    public async Task<bool> ExtendExpirationAsync(Guid couponId, DateTime newExpirationDate)
    {
        var coupon = await _couponRepository.GetByIdAsync(couponId);
        if (coupon == null || coupon.IsDeleted)
            return false;

        if (newExpirationDate <= DateTime.UtcNow)
            throw new InvalidOperationException("Yeni son kullanma tarihi gelecekte olmalıdır.");

        coupon.ExpirationDate = newExpirationDate;
        coupon.UpdatedAt = DateTime.UtcNow;

        _couponRepository.Update(coupon);
        await _couponRepository.SaveChangesAsync();

        await _couponRepository.AddToHistoryAsync(coupon, ChangeType.Updated, Guid.Empty);
        await _couponRepository.SaveChangesAsync();

        return true;
    }

    public async Task<bool> ResetUsageAsync(Guid couponId)
    {
        var success = await _couponRepository.ResetUsageAsync(couponId);

        if (success)
        {
            var coupon = await _couponRepository.GetByIdAsync(couponId);
            if (coupon != null)
            {
                await _couponRepository.AddToHistoryAsync(coupon, ChangeType.Updated, Guid.Empty);
                await _couponRepository.SaveChangesAsync();
            }
        }

        return success;
    }

    public async Task<bool> DeactivateCouponAsync(Guid couponId)
    {
        var coupon = await _couponRepository.GetByIdAsync(couponId);
        if (coupon == null || coupon.IsDeleted)
            return false;

        coupon.IsActive = false;
        coupon.UpdatedAt = DateTime.UtcNow;

        _couponRepository.Update(coupon);
        await _couponRepository.SaveChangesAsync();

        await _couponRepository.AddToHistoryAsync(coupon, ChangeType.Updated, Guid.Empty);
        await _couponRepository.SaveChangesAsync();

        return true;
    }

    public async Task<bool> ActivateCouponAsync(Guid couponId)
    {
        var coupon = await _couponRepository.GetByIdAsync(couponId);
        if (coupon == null || coupon.IsDeleted)
            return false;

        // Only activate if not expired and has remaining usage
        if (coupon.ExpirationDate <= DateTime.UtcNow)
            throw new InvalidOperationException("Süresi dolmuş kupon aktif edilemez.");

        if (coupon.TotalUsageLimit.HasValue && coupon.TotalUsageCount >= coupon.TotalUsageLimit.Value)
            throw new InvalidOperationException("Kullanım limitine ulaşmış kupon aktif edilemez.");

        coupon.IsActive = true;
        coupon.UpdatedAt = DateTime.UtcNow;

        _couponRepository.Update(coupon);
        await _couponRepository.SaveChangesAsync();

        await _couponRepository.AddToHistoryAsync(coupon, ChangeType.Updated, Guid.Empty);
        await _couponRepository.SaveChangesAsync();

        return true;
    }

    public async Task<List<Guid>> CreateBulkCouponsAsync(BulkCouponCreateDto dto)
    {
        var createdIds = new List<Guid>();

        foreach (var customerId in dto.CustomerIds)
        {
            var couponCode = $"{dto.CouponCodePrefix}-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";

            // Ensure unique coupon code
            while (!await _couponRepository.IsCouponCodeUniqueAsync(couponCode))
            {
                couponCode = $"{dto.CouponCodePrefix}-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
            }

            var coupon = new Coupon
            {
                Id = Guid.CreateVersion7(),
                CustomerId = customerId,
                CouponCode = couponCode,
                DiscountType = dto.DiscountType,
                DiscountAmount = dto.DiscountAmount,
                ExpirationDate = dto.ExpirationDate,
                TotalUsageLimit = 1, // BulkCouponCreateDto için varsayılan değer
                UsageLimitPerCustomer = 1, // BulkCouponCreateDto için varsayılan değer
                TotalUsageCount = 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _couponRepository.AddAsync(coupon);
            createdIds.Add(coupon.Id);
        }

        await _couponRepository.SaveChangesAsync();

        // Add to history for all created coupons
        foreach (var couponId in createdIds)
        {
            var coupon = await _couponRepository.GetByIdAsync(couponId);
            if (coupon != null)
            {
                await _couponRepository.AddToHistoryAsync(coupon, ChangeType.Created, Guid.Empty);
            }
        }
        await _couponRepository.SaveChangesAsync();

        return createdIds;
    }

    public async Task<bool> BulkDeleteAsync(List<Guid> couponIds)
    {
        return await _couponRepository.BulkDeleteAsync(couponIds);
    }

    public async Task<bool> BulkExtendExpirationAsync(List<Guid> couponIds, DateTime newExpirationDate)
    {
        if (newExpirationDate <= DateTime.UtcNow)
            throw new InvalidOperationException("Yeni son kullanma tarihi gelecekte olmalıdır.");

        return await _couponRepository.BulkUpdateExpirationAsync(couponIds, newExpirationDate);
    }

    public async Task<List<CouponListDto>> SearchAsync(CouponSearchDto searchDto)
    {
        var query = _couponRepository.Query().Include(c => c.Customer).Where(c => !c.IsDeleted);

        // Apply filters
        if (searchDto.CustomerId.HasValue)
            query = query.Where(c => c.CustomerId == searchDto.CustomerId.Value);

        if (!string.IsNullOrEmpty(searchDto.CouponCode))
            query = query.Where(c => c.CouponCode.Contains(searchDto.CouponCode));

        if (searchDto.DiscountType.HasValue)
            query = query.Where(c => c.DiscountType == searchDto.DiscountType.Value);

        if (searchDto.MinDiscountAmount.HasValue)
            query = query.Where(c => c.DiscountAmount >= searchDto.MinDiscountAmount.Value);

        if (searchDto.MaxDiscountAmount.HasValue)
            query = query.Where(c => c.DiscountAmount <= searchDto.MaxDiscountAmount.Value);

        if (searchDto.StartDate.HasValue)
            query = query.Where(c => c.CreatedAt >= searchDto.StartDate.Value);

        if (searchDto.EndDate.HasValue)
            query = query.Where(c => c.CreatedAt <= searchDto.EndDate.Value);

        if (searchDto.IsUsed.HasValue)
        {
            // IsUsed artık TotalUsageCount ile kontrol ediliyor
            if (searchDto.IsUsed.Value)
                query = query.Where(c => c.TotalUsageLimit.HasValue && c.TotalUsageCount >= c.TotalUsageLimit.Value);
            else
                query = query.Where(c => !c.TotalUsageLimit.HasValue || c.TotalUsageCount < c.TotalUsageLimit.Value);
        }

        if (searchDto.IsExpired.HasValue)
        {
            var now = DateTime.UtcNow;
            if (searchDto.IsExpired.Value)
                query = query.Where(c => c.ExpirationDate <= now);
            else
                query = query.Where(c => c.ExpirationDate > now);
        }

        if (searchDto.IsActive.HasValue)
        {
            var now = DateTime.UtcNow;
            if (searchDto.IsActive.Value)
                query = query.Where(c => c.IsActive && c.ExpirationDate > now && (!c.TotalUsageLimit.HasValue || c.TotalUsageCount < c.TotalUsageLimit.Value));
            else
                query = query.Where(c => !c.IsActive || c.ExpirationDate <= now || (c.TotalUsageLimit.HasValue && c.TotalUsageCount >= c.TotalUsageLimit.Value));
        }

        // Apply sorting
        query = searchDto.SortDirection?.ToLower() == "asc"
            ? query.OrderBy(c => c.CreatedAt)
            : query.OrderByDescending(c => c.CreatedAt);

        // Apply pagination
        if (searchDto.Page.HasValue && searchDto.PageSize.HasValue)
        {
            query = query.Skip((searchDto.Page.Value - 1) * searchDto.PageSize.Value)
                         .Take(searchDto.PageSize.Value);
        }

        var coupons = await query.ToListAsync();
        return coupons.Select(MapToCouponListDto).ToList();
    }

    public async Task<List<CouponListDto>> GetActiveCouponsAsync()
    {
        var coupons = await _couponRepository.GetActiveCouponsAsync();
        return coupons.Select(MapToCouponListDto).ToList();
    }

    public async Task<List<CouponListDto>> GetExpiredCouponsAsync()
    {
        var coupons = await _couponRepository.GetExpiredCouponsAsync();
        return coupons.Select(MapToCouponListDto).ToList();
    }

    public async Task<List<CouponListDto>> GetUsedCouponsAsync()
    {
        var coupons = await _couponRepository.GetUsedCouponsAsync();
        return coupons.Select(MapToCouponListDto).ToList();
    }

    public async Task<CouponAnalyticsDto> GetAnalyticsAsync()
    {
        var totalCoupons = await _couponRepository.GetTotalCouponsAsync();
        var activeCoupons = await _couponRepository.GetActiveCouponsCountAsync();
        var usedCoupons = await _couponRepository.GetUsedCouponsCountAsync();
        var expiredCoupons = await _couponRepository.GetExpiredCouponsCountAsync();
        var totalDiscountGiven = await _couponRepository.GetTotalDiscountGivenAsync();
        var averageDiscountAmount = await _couponRepository.GetAverageDiscountAmountAsync();

        var topCustomers = await GetTopCustomersAsync(10);
        var usageByDate = await GetUsageByDateAsync(30);

        return new CouponAnalyticsDto
        {
            TotalCoupons = totalCoupons,
            ActiveCoupons = activeCoupons,
            UsedCoupons = usedCoupons,
            ExpiredCoupons = expiredCoupons,
            TotalDiscountGiven = totalDiscountGiven,
            AverageDiscountAmount = averageDiscountAmount,
            TopCustomers = topCustomers,
            UsageByDate = usageByDate
        };
    }

    public async Task<List<CouponTopCustomersDto>> GetTopCustomersAsync(int count = 10)
    {
        var topCustomers = await _couponRepository.GetTopCustomersByCouponUsageAsync(count);
        var result = new List<CouponTopCustomersDto>();

        foreach (var coupon in topCustomers)
        {
            var customerCoupons = await _couponRepository.GetCustomerCouponsAsync(coupon.CustomerId ?? Guid.Empty);
            var usedCoupons = customerCoupons.Where(c => c.TotalUsageCount > 0).ToList();
            var totalDiscountUsed = usedCoupons.Sum(c => c.DiscountAmount * c.TotalUsageCount);

            result.Add(new CouponTopCustomersDto
            {
                CustomerId = coupon.CustomerId ?? Guid.Empty,
                CustomerName = _encryptionService.DecryptIfNotEmpty(coupon.Customer?.NameSurname) ?? "Bilinmeyen Müşteri",
                CouponCount = customerCoupons.Count(),
                UsedCouponCount = usedCoupons.Count(),
                TotalDiscountUsed = totalDiscountUsed
            });
        }

        return result;
    }

    public async Task<List<CouponUsageByDateDto>> GetUsageByDateAsync(int days = 30)
    {
        var startDate = DateTime.UtcNow.AddDays(-days).Date;
        var endDate = DateTime.UtcNow.Date.AddDays(1);

        var usageByDate = await _couponRepository.Query()
            .Where(c => !c.IsDeleted && c.CreatedAt >= startDate && c.CreatedAt < endDate)
            .GroupBy(c => c.CreatedAt.Date)
            .Select(g => new CouponUsageByDateDto
            {
                Date = g.Key,
                CouponsCreated = g.Count(),
                CouponsUsed = g.Count(c => c.TotalUsageCount > 0),
                TotalDiscountGiven = g.Where(c => c.TotalUsageCount > 0).Sum(c => c.DiscountAmount * c.TotalUsageCount)
            })
            .OrderBy(u => u.Date)
            .ToListAsync();

        return usageByDate;
    }

    public async Task<bool> IsCouponCodeUniqueAsync(string couponCode, Guid? excludeId = null)
    {
        return await _couponRepository.IsCouponCodeUniqueAsync(couponCode, excludeId);
    }

    public async Task<int> GetCouponUsageCountAsync(Guid couponId)
    {
        return await _couponRepository.GetCouponUsageCountAsync(couponId);
    }

    public async Task<bool> CanUseCouponAsync(Guid couponId, Guid customerId)
    {
        return await _couponRepository.CanUseCouponAsync(couponId, customerId);
    }

    public async Task<int> GetTotalCouponsAsync()
    {
        return await _couponRepository.GetTotalCouponsAsync();
    }

    public async Task<int> GetActiveCouponsCountAsync()
    {
        return await _couponRepository.GetActiveCouponsCountAsync();
    }

    public async Task<int> GetExpiredCouponsCountAsync()
    {
        return await _couponRepository.GetExpiredCouponsCountAsync();
    }

    public async Task<decimal> GetTotalDiscountGivenAsync()
    {
        return await _couponRepository.GetTotalDiscountGivenAsync();
    }

    // Yeni kupon sistemi metodları

    public async Task<List<CouponListDto>> GetGeneralCouponsAsync(int? page = null, int? pageSize = null)
    {
        List<Coupon> coupons;

        if (page.HasValue && pageSize.HasValue)
        {
            coupons = await _couponRepository.Query()
                .Where(c => !c.IsDeleted && c.CouponType == CouponType.General)
                .OrderByDescending(c => c.CreatedAt)
                .Skip((page.Value - 1) * pageSize.Value)
                .Take(pageSize.Value)
                .ToListAsync();
        }
        else
        {
            coupons = await _couponRepository.Query()
                .Where(c => !c.IsDeleted && c.CouponType == CouponType.General)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync();
        }

        return coupons.Select(MapToCouponListDto).ToList();
    }

    public async Task<List<CouponListDto>> GetCustomerSpecificCouponsAsync(int? page = null, int? pageSize = null)
    {
        List<Coupon> coupons;

        if (page.HasValue && pageSize.HasValue)
        {
            coupons = await _couponRepository.Query()
                .Where(c => !c.IsDeleted && c.CouponType == CouponType.CustomerSpecific)
                .Include(c => c.Customer)
                .OrderByDescending(c => c.CreatedAt)
                .Skip((page.Value - 1) * pageSize.Value)
                .Take(pageSize.Value)
                .ToListAsync();
        }
        else
        {
            coupons = await _couponRepository.Query()
                .Where(c => !c.IsDeleted && c.CouponType == CouponType.CustomerSpecific)
                .Include(c => c.Customer)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync();
        }

        return coupons.Select(MapToCouponListDto).ToList();
    }

    public async Task<List<CouponUsageDto>> GetCouponUsageHistoryAsync(Guid couponId)
    {
        var usageHistory = await _couponRepository.GetCouponUsageHistoryAsync(couponId);

        return usageHistory.Select(usage => new CouponUsageDto
        {
            CouponId = usage.CouponId,
            CustomerId = usage.CustomerId,
            CouponCode = usage.Coupon.CouponCode,
            OrderId = usage.OrderId,
            DiscountApplied = usage.DiscountApplied,
            OrderAmount = usage.OrderAmount
        }).ToList();
    }

    public async Task<List<CouponUsageDto>> GetCustomerCouponUsageHistoryAsync(Guid customerId)
    {
        var usageHistory = await _couponRepository.GetCustomerCouponUsageHistoryAsync(customerId);

        return usageHistory.Select(usage => new CouponUsageDto
        {
            CouponId = usage.CouponId,
            CustomerId = usage.CustomerId,
            CouponCode = usage.Coupon.CouponCode,
            OrderId = usage.OrderId,
            DiscountApplied = usage.DiscountApplied,
            OrderAmount = usage.OrderAmount
        }).ToList();
    }

    // Kupon sipariş doğrulama metodu

    public async Task<CouponValidationDto> ValidateCouponForOrderAsync(string couponCode, Guid customerId, decimal orderAmount)
    {
        var validation = await ValidateCouponAsync(couponCode, customerId);
        if (!validation.IsValid)
        {
            return validation;
        }

        // Sipariş tutarına göre indirim hesapla
        var coupon = await _couponRepository.GetByCouponCodeAsync(couponCode);
        if (coupon == null)
        {
            return new CouponValidationDto
            {
                IsValid = false,
                Message = "Kupon bulunamadı"
            };
        }

        decimal discountAmount;
        if (coupon.DiscountType == DiscountType.Percentage)
        {
            discountAmount = orderAmount * (coupon.DiscountAmount / 100);
        }
        else
        {
            discountAmount = coupon.DiscountAmount;
        }

        // İndirim tutarı sipariş tutarından fazla olamaz
        if (discountAmount > orderAmount)
        {
            discountAmount = orderAmount;
        }

        return new CouponValidationDto
        {
            IsValid = true,
            Message = "Kupon geçerli",
            DiscountAmount = discountAmount,
            DiscountType = coupon.DiscountType
        };
    }
}
