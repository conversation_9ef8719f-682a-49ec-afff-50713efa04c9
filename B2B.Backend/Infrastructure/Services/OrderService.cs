using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;
using Infrastructure.Extensions;

namespace Infrastructure.Services;

public class OrderService : IOrderService
{
    private readonly IGenericRepository<Order> _orderRepository;
    private readonly IGenericRepository<OrderRow> _orderRowRepository;
    private readonly IGenericRepository<Customer> _customerRepository;
    private readonly IGenericRepository<Product> _productRepository;
    private readonly IUserPointService _userPointService;
    private readonly ICouponRepository _couponRepository;

    public OrderService(
        IGenericRepository<Order> orderRepository,
        IGenericRepository<OrderRow> orderRowRepository,
        IGenericRepository<Customer> customerRepository,
        IGenericRepository<Product> productRepository,
        IUserPointService userPointService,
        ICouponRepository couponRepository)
    {
        _orderRepository = orderRepository;
        _orderRowRepository = orderRowRepository;
        _customerRepository = customerRepository;
        _productRepository = productRepository;
        _userPointService = userPointService;
        _couponRepository = couponRepository;
    }

    public async Task<List<OrderListDto>> GetListAsync(int? page = null, int? pageSize = null)
    {
        var query = _orderRepository.Query()
            .Include(o => o.Customer)
            .Include(o => o.OrderRows)
            .Where(o => !o.IsDeleted)
            .OrderByDescending(o => o.CreatedAt);

        if (page.HasValue && pageSize.HasValue)
        {
            query = (IOrderedQueryable<Order>)query.Skip((page.Value - 1) * pageSize.Value).Take(pageSize.Value);
        }

        var orders = await query.ToListAsync();

        return orders.Select(o => new OrderListDto
        {
            Id = o.Id,
            OrderNumber = o.OrderNumber,
            Status = o.Status,
            TotalAmount = o.TotalAmount,
            CouponDiscountAmount = o.CouponDiscountAmount,
            UsedPoints = o.UsedPoints,
            CreatedAt = o.CreatedAt,
            CustomerName = o.Customer.NameSurname.Decrypt(),
            ItemCount = o.OrderRows.Count
        }).ToList();
    }

    public async Task<OrderDto?> GetByIdAsync(Guid id)
    {
        var order = await _orderRepository.Query()
            .Include(o => o.Customer)
            .Include(o => o.Address)
            .Include(o => o.OrderRows)
                .ThenInclude(or => or.Product)
                .ThenInclude(p=> p.Images)
            .Include(o => o.Payments)
            .Include(o => o.Shipments)
                .ThenInclude(s => s.Carrier)
            .FirstOrDefaultAsync(o => o.Id == id && !o.IsDeleted);

        if (order == null) return null;

        return new OrderDto
        {
            Id = order.Id,
            CustomerId = order.CustomerId,
            AddressId = order.AddressId,
            OrderNumber = order.OrderNumber,
            Status = order.Status,
            GrossTotalAmount = order.GrossTotalAmount,
            TotalAmount = order.TotalAmount,
            DiscountAmount = order.DiscountAmount,
            ShippingAmount = order.ShippingAmount,
            TaxAmount = order.TaxAmount,
            Notes = order.Notes,
            CouponId = order.CouponId,
            CouponCode = order.CouponCode,
            CouponDiscountAmount = order.CouponDiscountAmount,
            UsedPoints = order.UsedPoints,
            CreatedAt = order.CreatedAt,
            UpdatedAt = order.UpdatedAt,
            Customer = order.Customer != null ? new CustomerDto
            {
                Id = order.Customer.Id,
                NameSurname = order.Customer.NameSurname.Decrypt(),
                PhoneNumber = order.Customer.PhoneNumber.DecryptIfNotEmpty(),
                Email = order.Customer.Email.DecryptIfNotEmpty(),
                TaxOrIdentityNumber = order.Customer.TaxOrIdentityNumber.DecryptIfNotEmpty(),
                TaxOffice = order.Customer.TaxOffice
            } : null,
            Address = order.Address != null ? new AddressDto
            {
                Id = order.Address.Id,
                Name = order.Address.Name,
                Line1 = order.Address.Line1,
                Line2 = order.Address.Line2,
                City = order.Address.City,
                District = order.Address.District,
                Country = order.Address.Country,
                PostalCode = order.Address.PostalCode,
                AddressType = order.Address.AddressType,
                IsDefault = order.Address.IsDefault,
                CustomerId = order.Address.CustomerId,
                DealerId = order.Address.DealerId,
                CreatedAt = order.Address.CreatedAt,
                UpdatedAt = order.Address.UpdatedAt
            } : null,
            OrderRows = order.OrderRows.Select(or => new OrderRowDto
            {
                Id = or.Id,
                OrderId = or.OrderId,
                ProductId = or.ProductId,
                Quantity = or.Quantity,
                Price = or.Price,
                DiscountedPrice = or.DiscountedPrice,
                Product = or.Product != null ? new ProductDto
                {
                    Id = or.Product.Id,
                    Name = or.Product.Name,
                    Description = or.Product.Description,
                    Sku = or.Product.Sku,
                    Price = or.Product.Price,
                    StockQuantity = or.Product.StockQuantity,
                    Images = or.Product.Images.Where(i=>i.IsMainImage).Select(i => new ProductImageDto
                    {
                        Id = i.Id,
                        ProductId = i.ProductId,
                        OriginalImagePath = i.OriginalPath,
                        ThumbnailSmallPath = i.ThumbnailSmPath,
                        ThumbnailMediumPath = i.ThumbnailMdPath,
                        FileName = i.Name,
                        AltText = i.AltText,
                        OriginalWidth = i.OriginalWidth,
                        OriginalHeight = i.OriginalHeight,
                        FileSizeBytes = i.FileSizeBytes,
                        IsMainImage = i.IsMainImage,
                        SortOrder = i.SortOrder
                    }).ToList()
                } : null
            }).ToList(),
            Payments = order.Payments.Select(p => new PaymentDto
            {
                Id = p.Id,
                OrderId = p.OrderId,
                Amount = p.Amount,
                Description = p.Description,
                PaymentMethod = p.PaymentMethod,
                Status = p.Status,
                MaskedCardNumber = p.MaskedCardNumber,
                PaymentResponse = p.PaymentResponse,
                PaymentResponseCode = p.PaymentResponseCode,
                PaymentResponseMessage = p.PaymentResponseMessage,
                PaymentResponseTransactionId = p.PaymentResponseTransactionId,
                CreatedAt = p.CreatedAt
            }).ToList(),
            Shipments = order.Shipments.Select(s => new ShipmentDto
            {
                Id = s.Id,
                OrderId = s.OrderId,
                TrackingNumber = s.TrackingNumber,
                CarrierId = s.Carrier.Id,
                CarrierName = s.Carrier.Name,
                CarrierShortCode = s.Carrier.ShortCode,
                Status = s.Status,
                ShippedAt = s.ShippedAt,
                DeliveredAt = s.DeliveredAt,
                Notes = s.Notes,
                CreatedAt = s.CreatedAt
            }).ToList()
        };
    }

    public async Task<OrderDto?> GetByOrderNumberAsync(string orderNumber)
    {
        var order = await _orderRepository.Query()
            .Include(o => o.Customer)
            .Include(o => o.Address)
            .Include(o => o.OrderRows)
                .ThenInclude(or => or.Product)
            .Include(o => o.Payments)
            .Include(o => o.Shipments)
                .ThenInclude(s => s.Carrier)
            .FirstOrDefaultAsync(o => o.OrderNumber == orderNumber && !o.IsDeleted);

        if (order == null) return null;

        return await GetByIdAsync(order.Id);
    }

    public async Task<Guid> CreateAsync(OrderCreateDto dto)
    {
        // Generate order number
        var orderNumber = await GenerateOrderNumberAsync();

        var order = new Order
        {
            Id = Guid.CreateVersion7(),
            CustomerId = dto.CustomerId,
            AddressId = dto.AddressId,
            OrderNumber = orderNumber,
            Status = OrderStatus.Pending,
            GrossTotalAmount = dto.GrossTotalAmount, // Brüt tutar (indirim öncesi)
            TotalAmount = dto.TotalAmount, // Net tutar (indirim sonrası)
            DiscountAmount = dto.DiscountAmount, // İndirim tutarı
            ShippingAmount = dto.ShippingAmount,
            TaxAmount = dto.TaxAmount, // Vergi tutarı
            Notes = dto.Notes,
            CouponId = dto.CouponId,
            CouponCode = dto.CouponCode,
            CouponDiscountAmount = dto.CouponDiscountAmount,
            UsedPoints = dto.UsedPoints,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _orderRepository.AddAsync(order);
        await _orderRepository.SaveChangesAsync();

        // Add order rows and calculate points
        decimal totalPointsToEarn = 0;
        foreach (var rowDto in dto.OrderRows)
        {
            var product = await _productRepository.GetByIdAsync(rowDto.ProductId);
            if (product == null) continue;

            var orderRow = new OrderRow
            {
                Id = Guid.CreateVersion7(),
                OrderId = order.Id,
                ProductId = rowDto.ProductId,
                Quantity = rowDto.Quantity,
                Price = rowDto.Price,
                DiscountedPrice = rowDto.DiscountedPrice,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _orderRowRepository.AddAsync(orderRow);
            await _orderRowRepository.SaveChangesAsync();

            // Update stock
            product.StockQuantity -= orderRow.Quantity;
            _productRepository.Update(product);
            await _productRepository.SaveChangesAsync();

            // Calculate points for this product
            if (product.PointValue.HasValue && product.PointValue.Value > 0)
            {
                totalPointsToEarn += product.PointValue.Value * rowDto.Quantity;
            }
        }

        // Add pending points if there are any points to earn
        if (totalPointsToEarn > 0)
        {
            try
            {
                var pointDescription = $"Sipariş #{order.OrderNumber} için puan kazancı";
                await _userPointService.AddPendingPointsAsync(order.CustomerId, totalPointsToEarn, pointDescription, order.Id);
            }
            catch (Exception ex)
            {
                // Log error but don't fail the order creation
                // TODO: Add proper logging
                Console.WriteLine($"Error adding pending points for order {order.OrderNumber}: {ex.Message}");
            }
        }

        // Kupon kullanım kaydı oluştur (eğer kupon kullanıldıysa)
        if (order.CouponId.HasValue && order.CouponDiscountAmount > 0)
        {
            try
            {
                var couponUsage = new CouponUsage
                {
                    Id = Guid.CreateVersion7(),
                    CouponId = order.CouponId.Value,
                    CustomerId = order.CustomerId,
                    OrderId = order.Id,
                    DiscountApplied = order.CouponDiscountAmount,
                    OrderAmount = order.TotalAmount,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                await _couponRepository.CreateCouponUsageAsync(couponUsage);

                // Kuponun toplam kullanım sayısını artır
                var coupon = await _couponRepository.GetByIdAsync(order.CouponId.Value);
                if (coupon != null)
                {
                    coupon.TotalUsageCount++;
                    _couponRepository.Update(coupon);
                    await _couponRepository.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                // Log error but don't fail the order creation
                // TODO: Add proper logging
                Console.WriteLine($"Error creating coupon usage for order {order.OrderNumber}: {ex.Message}");
            }
        }

        return order.Id;
    }

    public async Task UpdateAsync(OrderUpdateDto dto)
    {
        var order = await _orderRepository.GetByIdAsync(dto.Id);
        if (order == null || order.IsDeleted)
            throw new ArgumentException("Order not found");

        order.Status = dto.Status;
        order.DiscountAmount = dto.DiscountAmount;
        order.ShippingAmount = dto.ShippingAmount;
        order.TaxAmount = dto.TaxAmount;
        order.Notes = dto.Notes;
        order.UpdatedAt = DateTime.UtcNow;

        // Recalculate total amount
        var orderRows = await _orderRowRepository.Query()
            .Where(or => or.OrderId == order.Id && !or.IsDeleted)
            .ToListAsync();

        order.TotalAmount = orderRows.Sum(or => or.DiscountedPrice * or.Quantity)
                           + order.ShippingAmount + order.TaxAmount - order.DiscountAmount;

        _orderRepository.Update(order);
        await _orderRepository.SaveChangesAsync();
    }

    public async Task UpdateStatusAsync(OrderStatusUpdateDto dto)
    {
        var order = await _orderRepository.GetByIdAsync(dto.Id);
        if (order == null || order.IsDeleted)
            throw new ArgumentException("Order not found");

        order.Status = dto.Status;
        if (!string.IsNullOrEmpty(dto.Notes))
            order.Notes = dto.Notes;
        order.UpdatedAt = DateTime.UtcNow;

        _orderRepository.Update(order);
        await _orderRepository.SaveChangesAsync();
    }

    public async Task DeleteAsync(Guid id)
    {
        var order = await _orderRepository.GetByIdAsync(id);
        if (order == null || order.IsDeleted)
            throw new ArgumentException("Order not found");

        order.IsDeleted = true;
        order.UpdatedAt = DateTime.UtcNow;

        _orderRepository.Update(order);
        await _orderRepository.SaveChangesAsync();
    }

    public async Task<List<OrderListDto>> GetOrdersByCustomerAsync(Guid customerId)
    {
        var orders = await _orderRepository.Query()
            .Include(o => o.Customer)
            .Include(o => o.OrderRows)
            .Where(o => o.CustomerId == customerId && !o.IsDeleted)
            .OrderByDescending(o => o.CreatedAt)
            .ToListAsync();

        return orders.Select(o => new OrderListDto
        {
            Id = o.Id,
            OrderNumber = o.OrderNumber,
            Status = o.Status,
            TotalAmount = o.TotalAmount,
            CouponDiscountAmount = o.CouponDiscountAmount,
            UsedPoints = o.UsedPoints,
            CreatedAt = o.CreatedAt,
            CustomerName = o.Customer.NameSurname,
            ItemCount = o.OrderRows.Count
        }).ToList();
    }

    public async Task<List<OrderListDto>> GetOrdersByStatusAsync(OrderStatus status)
    {
        var orders = await _orderRepository.Query()
            .Include(o => o.Customer)
            .Include(o => o.OrderRows)
            .Where(o => o.Status == status && !o.IsDeleted)
            .OrderByDescending(o => o.CreatedAt)
            .ToListAsync();

        return orders.Select(o => new OrderListDto
        {
            Id = o.Id,
            OrderNumber = o.OrderNumber,
            Status = o.Status,
            TotalAmount = o.TotalAmount,
            CouponDiscountAmount = o.CouponDiscountAmount,
            UsedPoints = o.UsedPoints,
            CreatedAt = o.CreatedAt,
            CustomerName = o.Customer.NameSurname,
            ItemCount = o.OrderRows.Count
        }).ToList();
    }

    public async Task<List<OrderListDto>> SearchOrdersAsync(string searchTerm)
    {
        var orders = await _orderRepository.Query()
            .Include(o => o.Customer)
            .Include(o => o.OrderRows)
            .Where(o => !o.IsDeleted &&
                       (o.OrderNumber.Contains(searchTerm) ||
                        o.Customer.NameSurname.Contains(searchTerm) ||
                        o.Customer.Email!.Contains(searchTerm)))
            .OrderByDescending(o => o.CreatedAt)
            .ToListAsync();

        return orders.Select(o => new OrderListDto
        {
            Id = o.Id,
            OrderNumber = o.OrderNumber,
            Status = o.Status,
            TotalAmount = o.TotalAmount,
            CouponDiscountAmount = o.CouponDiscountAmount,
            UsedPoints = o.UsedPoints,
            CreatedAt = o.CreatedAt,
            CustomerName = o.Customer.NameSurname,
            ItemCount = o.OrderRows.Count
        }).ToList();
    }

    public async Task<decimal> GetTotalSalesAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _orderRepository.Query()
            .Where(o => !o.IsDeleted && o.Status != OrderStatus.Cancelled);

        if (startDate.HasValue)
            query = query.Where(o => o.CreatedAt >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(o => o.CreatedAt <= endDate.Value);

        return await query.SumAsync(o => o.TotalAmount);
    }

    public async Task<int> GetOrderCountAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _orderRepository.Query()
            .Where(o => !o.IsDeleted);

        if (startDate.HasValue)
            query = query.Where(o => o.CreatedAt >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(o => o.CreatedAt <= endDate.Value);

        return await query.CountAsync();
    }

    public async Task<bool> HasCustomerOrderedProductAsync(Guid customerId, Guid productId)
    {
        try
        {
            var hasOrdered = await _orderRepository.Query()
                .Include(o => o.OrderRows)
                .Where(o => o.CustomerId == customerId && !o.IsDeleted)
                .Where(o => o.Status == OrderStatus.Delivered)
                .AnyAsync(o => o.OrderRows.Any(or => or.ProductId == productId && !or.IsDeleted));

            return hasOrdered;
        }
        catch (Exception ex)
        {
            throw new Exception($"Error checking if customer ordered product: {ex.Message}", ex);
        }
    }

    public async Task<OrderTimelineDto?> GetTimelineAsync(Guid orderId)
    {
        var order = await _orderRepository.Query()
            .Include(o => o.Payments)
            .Include(o => o.Shipments)
                .ThenInclude(s => s.Carrier)
            .FirstOrDefaultAsync(o => o.Id == orderId && !o.IsDeleted);

        if (order == null) return null;

        var events = new List<OrderTimelineEventDto>();

        // Sipariş oluşturulma eventi
        events.Add(new OrderTimelineEventDto
        {
            Id = Guid.CreateVersion7(),
            EventType = "ORDER_CREATED",
            Title = "Sipariş Oluşturuldu",
            Description = $"Sipariş #{order.OrderNumber} başarıyla oluşturuldu",
            EventDate = order.CreatedAt,
            Status = order.Status.ToString(),
            Notes = order.Notes,
            Metadata = new Dictionary<string, object>
            {
                ["totalAmount"] = order.TotalAmount,
                ["status"] = order.Status.ToString()
            }
        });

        // Ödeme eventleri
        foreach (var payment in order.Payments.OrderBy(p => p.CreatedAt))
        {
            var eventType = payment.Status switch
            {
                PaymentStatus.Completed => "PAYMENT_COMPLETED",
                PaymentStatus.Failed => "PAYMENT_FAILED",
                PaymentStatus.Cancelled => "PAYMENT_CANCELLED",
                PaymentStatus.Refunded => "PAYMENT_REFUNDED",
                _ => "PAYMENT_PROCESSING"
            };

            var title = payment.Status switch
            {
                PaymentStatus.Completed => "Ödeme Tamamlandı",
                PaymentStatus.Failed => "Ödeme Başarısız",
                PaymentStatus.Cancelled => "Ödeme İptal Edildi",
                PaymentStatus.Refunded => "Ödeme İade Edildi",
                _ => "Ödeme İşleniyor"
            };

            events.Add(new OrderTimelineEventDto
            {
                Id = payment.Id,
                EventType = eventType,
                Title = title,
                Description = $"{payment.Amount}₺ tutarında {payment.PaymentMethod} ile ödeme",
                EventDate = payment.CreatedAt,
                Status = payment.Status.ToString(),
                Notes = payment.Description,
                Metadata = new Dictionary<string, object>
                {
                    ["amount"] = payment.Amount,
                    ["paymentMethod"] = payment.PaymentMethod,
                    ["maskedCardNumber"] = payment.MaskedCardNumber,
                    ["transactionId"] = payment.PaymentResponseTransactionId ?? ""
                }
            });
        }

        // Kargo eventleri
        foreach (var shipment in order.Shipments.OrderBy(s => s.CreatedAt))
        {
            var eventType = shipment.Status switch
            {
                ShipmentStatus.Shipped => "SHIPMENT_SHIPPED",
                ShipmentStatus.InTransit => "SHIPMENT_IN_TRANSIT",
                ShipmentStatus.Delivered => "SHIPMENT_DELIVERED",
                ShipmentStatus.Cancelled => "SHIPMENT_CANCELLED",
                ShipmentStatus.Returned => "SHIPMENT_RETURNED",
                _ => "SHIPMENT_PROCESSING"
            };

            var title = shipment.Status switch
            {
                ShipmentStatus.Shipped => "Kargo Verildi",
                ShipmentStatus.InTransit => "Kargo Yolda",
                ShipmentStatus.Delivered => "Kargo Teslim Edildi",
                ShipmentStatus.Cancelled => "Kargo İptal Edildi",
                ShipmentStatus.Returned => "Kargo İade Edildi",
                _ => "Kargo Hazırlanıyor"
            };

            events.Add(new OrderTimelineEventDto
            {
                Id = shipment.Id,
                EventType = eventType,
                Title = title,
                Description = $"{shipment.Carrier.Name} ile kargo gönderildi",
                EventDate = shipment.Status == ShipmentStatus.Delivered && shipment.DeliveredAt.HasValue
                    ? shipment.DeliveredAt.Value
                    : shipment.CreatedAt,
                Status = shipment.Status.ToString(),
                Notes = shipment.Notes,
                Metadata = new Dictionary<string, object>
                {
                    ["trackingNumber"] = shipment.TrackingNumber ?? "",
                    ["carrierName"] = shipment.Carrier.Name,
                    ["cargoKey"] = shipment.CargoKey
                }
            });
        }

        // Sipariş durum değişiklikleri (eğer varsa)
        if (order.Status == OrderStatus.Cancelled)
        {
            events.Add(new OrderTimelineEventDto
            {
                Id = Guid.CreateVersion7(),
                EventType = "ORDER_CANCELLED",
                Title = "Sipariş İptal Edildi",
                Description = "Sipariş iptal edildi",
                EventDate = order.UpdatedAt,
                Status = order.Status.ToString(),
                Notes = order.Notes
            });
        }
        else if (order.Status == OrderStatus.Delivered)
        {
            events.Add(new OrderTimelineEventDto
            {
                Id = Guid.CreateVersion7(),
                EventType = "ORDER_DELIVERED",
                Title = "Sipariş Teslim Edildi",
                Description = "Sipariş başarıyla teslim edildi",
                EventDate = order.UpdatedAt,
                Status = order.Status.ToString(),
                Notes = order.Notes
            });
        }

        // Eventleri tarihe göre sırala
        events = events.OrderBy(e => e.EventDate).ToList();

        return new OrderTimelineDto
        {
            OrderId = order.Id,
            OrderNumber = order.OrderNumber,
            Events = events
        };
    }

    private async Task<string> GenerateOrderNumberAsync()
    {
        var today = DateTime.UtcNow;
        var prefix = $"ORD{today:yyyyMMdd}";

        var lastOrder = await _orderRepository.Query()
            .Where(o => o.OrderNumber.StartsWith(prefix))
            .OrderByDescending(o => o.OrderNumber)
            .FirstOrDefaultAsync();

        if (lastOrder == null)
        {
            return $"{prefix}001";
        }

        var lastNumber = lastOrder.OrderNumber.Substring(prefix.Length);
        if (int.TryParse(lastNumber, out var number))
        {
            return $"{prefix}{(number + 1):D3}";
        }

        return $"{prefix}001";
    }
}
