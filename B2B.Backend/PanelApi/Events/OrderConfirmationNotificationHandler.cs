using Application.Contracts.Interfaces;
using Core.Events;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace PanelApi.Events;

/// <summary>
/// Sipariş onay maili event handler'ı
/// MailAPI'ye SendMailRequested event'i gönderir (döngü desteği ile)
/// </summary>
public class OrderConfirmationNotificationHandler : IConsumer<OrderConfirmationNotificationRequested>
{
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly IMailTemplateService _mailTemplateService;
    private readonly IMailLinkService _mailLinkService;
    private readonly ILogger<OrderConfirmationNotificationHandler> _logger;

    public OrderConfirmationNotificationHandler(
        IPublishEndpoint publishEndpoint,
        IMailTemplateService mailTemplateService,
        IMailLinkService mailLinkService,
        ILogger<OrderConfirmationNotificationHandler> logger)
    {
        _publishEndpoint = publishEndpoint;
        _mailTemplateService = mailTemplateService;
        _mailLinkService = mailLinkService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<OrderConfirmationNotificationRequested> context)
    {
        var notification = context.Message;

        try
        {
            _logger.LogInformation("Processing order confirmation notification for: {CustomerEmail}",
                notification.CustomerEmail);

            // Basit değişkenler
            var variables = new Dictionary<string, string>
            {
                { "customerName", notification.CustomerName },
                { "customerPhone", notification.CustomerPhone },
                { "customerEmail", notification.CustomerEmail },
                { "orderNumber", notification.OrderNumber },
                { "orderDate", notification.OrderDate.ToString("dd.MM.yyyy") },
                { "orderItemsTotal", notification.OrderSubtotal.ToString("F2") },
                { "orderItemsDiscountTotal", notification.DiscountAmount.ToString("F2") },
                { "orderItemsCargo", notification.ShippingAmount.ToString("F2") },
                { "orderItemsKdvTotal", notification.OrderTotalAmount.ToString("F2") },
                { "deliveryAddress", notification.DeliveryAddress },
                { "deliveryCity", notification.DeliveryCity },
                { "deliveryCounty", notification.DeliveryCounty },
                { "deliveryCountry", notification.DeliveryCountry },
                { "billingAddress", notification.BillingAddress },
                { "billingCity", notification.BillingCity },
                { "billingCounty", notification.BillingCounty },
                { "billingCountry", notification.BillingCountry },
                { "sendLinkAddress", _mailLinkService.GetOrderDetailLink(notification.OrderId) },
                { "couponCode", notification.CouponCode ?? "Kupon Kullanılmadı" },
                { "usedPoints", notification.UsedPoints.ToString() }
            };

            // Döngü verileri - sipariş ürünleri
            var loopData = new Dictionary<string, List<Dictionary<string, string>>>
            {
                {
                    "items", notification.OrderItems.Select(item => new Dictionary<string, string>
                    {
                        { "orderRowName", item.ProductName },
                        { "orderRowImage", item.ProductImageUrl },
                        { "orderRowAmount", item.Quantity.ToString() },
                        { "orderRowPrice", item.DiscountedPrice.ToString("F2") }, // İndirimli fiyat (görünen fiyat)
                        { "orderRowDiscount", item.UnitPrice.ToString("F2") }, // Orijinal fiyat (çizgili gösterilecek)
                        { "orderRowVariant", item.VariantInfo ?? "" }
                    }).ToList()
                }
            };

            // Template'i döngü desteği ile render et
            var (subject, content) = await _mailTemplateService.RenderTemplateWithLoopsAsync(
                "order-confirmation", 
                variables, 
                loopData
            );

            // MailAPI'ye SendMailRequested event'i gönder
            await _publishEndpoint.Publish(new SendMailRequested
            {
                TemplateShortCode = "order-confirmation",
                ToEmail = notification.CustomerEmail,
                ToName = notification.CustomerName,
                Variables = variables,
                Priority = 1, // Yüksek öncelik
                RelatedEntityId = notification.OrderId,
                RelatedEntityType = "Order",
                // Render edilmiş içeriği de gönderebiliriz
                CustomSubject = subject,
                CustomContent = content
            });

            _logger.LogInformation("SendMailRequested event published for order confirmation: {CustomerEmail}",
                notification.CustomerEmail);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing order confirmation notification for: {CustomerEmail}",
                notification.CustomerEmail);
            throw;
        }
    }
}
