'use client';

import { useTranslations } from 'next-intl';
import { useOrder, useUpdateOrderStatus } from '@/lib/api/hooks/useOrders';
import { useShipmentsByOrder } from '@/lib/api/hooks/useShipping';
import { OrderStatus, ShipmentStatus } from '@/types/order';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { Package, User, CreditCard, Truck, FileText } from 'lucide-react';


// Import sub-components
import OrderHeader from './OrderHeader';
import OrderItemCard from './OrderItemCard';
import OrderSummary from './OrderSummary';
import CustomerInfoCard from './CustomerInfoCard';
import StatusManagementCard from './StatusManagementCard';
import OrderTimeline from './OrderTimeline';
import PromoCodeCard from './PromoCodeCard';
import ShippingManagementCard from './ShippingManagementCard';

interface OrderDetailClientProps {
  orderId: string;
  canRead: boolean;
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

export default function OrderDetailClient({
  orderId,
  canUpdate,
}: OrderDetailClientProps) {
  const t = useTranslations('order');
  const { data: order, isLoading, error } = useOrder(orderId);
  const { data: shipments = [] } = useShipmentsByOrder(orderId);
  const updateStatusMutation = useUpdateOrderStatus();

  const getOrderStatusBadge = (status: OrderStatus) => {
    const statusConfig = {
      [OrderStatus.Pending]: { label: t('status.pending'), variant: 'secondary' as const },
      [OrderStatus.Processing]: { label: t('status.processing'), variant: 'default' as const },
      [OrderStatus.Shipped]: { label: t('status.shipped'), variant: 'outline' as const },
      [OrderStatus.Delivered]: { label: t('status.delivered'), variant: 'default' as const },
      [OrderStatus.Cancelled]: { label: t('status.cancelled'), variant: 'destructive' as const },
      [OrderStatus.Refunded]: { label: t('status.refunded'), variant: 'secondary' as const },
    };

    const config = statusConfig[status];
    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };



  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('tr-TR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(dateString));
  };

  if (isLoading) {
    return (
      <Card className="dark:bg-gray-800 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="text-center dark:text-white">Yükleniyor...</div>
        </CardContent>
      </Card>
    );
  }

  if (error || !order) {
    return (
      <Card className="dark:bg-gray-800 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="text-center text-red-500 dark:text-red-400">
            Sipariş yüklenirken bir hata oluştu
          </div>
        </CardContent>
      </Card>
    );
  }

  const handleOrderStatusUpdate = async (status: OrderStatus) => {
    try {
      await updateStatusMutation.mutateAsync({
        id: order.id,
        status: status,
      });
      toast.success('Sipariş durumu başarıyla güncellendi');
    } catch {
      toast.error('Sipariş durumu güncellenirken bir hata oluştu');
    }
  };

  const handleShipmentStatusUpdate = async (_status: ShipmentStatus) => {
    try {
      // TODO: Shipment status update API'si eklenecek
      toast.success('Kargo durumu başarıyla güncellendi');
    } catch {
      toast.error('Kargo durumu güncellenirken bir hata oluştu');
    }
  };

  return (
    <div className="space-y-6">
      {/* Order Header */}
      <OrderHeader
        orderNumber={order.orderNumber}
        createdAt={order.createdAt}
        status={order.status}
        formatDate={formatDate}
        getOrderStatusBadge={getOrderStatusBadge}
      />

      <Tabs defaultValue="details" className="space-y-6">
        {/* Tabs Navigation */}
        <div className="overflow-x-auto">
          <TabsList className="inline-flex h-auto p-1 bg-muted dark:bg-gray-800 rounded-lg min-w-max">
            <TabsTrigger value="details" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
              <FileText className="h-4 w-4" />
              <span className="whitespace-nowrap">Sipariş Detayları</span>
            </TabsTrigger>
            <TabsTrigger value="customer" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
              <User className="h-4 w-4" />
              <span className="whitespace-nowrap">Müşteri Bilgileri</span>
            </TabsTrigger>
            <TabsTrigger value="shipping" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
              <Truck className="h-4 w-4" />
              <span className="whitespace-nowrap">Kargo Yönetimi</span>
            </TabsTrigger>
            {canUpdate && (
              <TabsTrigger value="management" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
                <Package className="h-4 w-4" />
                <span className="whitespace-nowrap">Durum Yönetimi</span>
              </TabsTrigger>
            )}
          </TabsList>
        </div>

        {/* Tab Content */}
        <Card className="dark:bg-gray-800 dark:border-gray-700">
          <CardContent className="p-6">
            <TabsContent value="details" className="space-y-6 mt-0">
              {/* Order Items */}
              <div className="space-y-4">
                {order.orderRows.map((item) => (
                  <OrderItemCard
                    key={item.id}
                    item={item}
                    formatCurrency={formatCurrency}
                  />
                ))}
              </div>

              {/* Promo Code Section */}
              <PromoCodeCard
                promoCode={order.couponCode || null}
                discountAmount={order.couponDiscountAmount}
              />

              {/* Order Summary */}
              <OrderSummary order={order} formatCurrency={formatCurrency} />
            </TabsContent>

            <TabsContent value="customer" className="mt-0">
              <CustomerInfoCard
                customer={order.customer || null}
                address={order.address || null}
                payments={order.payments}
                orderCount={order.orderRows.length}
                canUpdate={canUpdate}
              />
            </TabsContent>

            <TabsContent value="shipping" className="mt-0">
              <ShippingManagementCard
                order={order}
                shipments={shipments}
                canUpdate={canUpdate}
              />
            </TabsContent>

            {canUpdate && (
              <TabsContent value="management" className="mt-0">
                <StatusManagementCard
                  order={order}
                  onOrderStatusUpdate={handleOrderStatusUpdate}
                  onShipmentStatusUpdate={handleShipmentStatusUpdate}
                  isUpdating={updateStatusMutation.isPending}
                />
              </TabsContent>
            )}
          </CardContent>
        </Card>
      </Tabs>

      <OrderTimeline orderId={order.id} />
    </div>
  );
}
