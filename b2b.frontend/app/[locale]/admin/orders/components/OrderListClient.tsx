'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useOrders, useDeleteOrder } from '@/lib/api/hooks/useOrders';
import { OrderStatus } from '@/types/order';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Card, CardContent, CardHeader, <PERSON>Title } from '@/components/ui/card';
import { Eye, Edit, Trash2, Search } from 'lucide-react';
import { toast } from 'sonner';

interface OrderListClientProps {
  canRead: boolean;
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

export default function OrderListClient({
  canRead,
  canCreate,
  canUpdate,
  canDelete,
}: OrderListClientProps) {
  const t = useTranslations('order');
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<OrderStatus | 'all'>('all');
  const [page, setPage] = useState(1);
  const pageSize = 20;

  const { data: orders = [], isLoading, error } = useOrders({
    page,
    pageSize,
    ...(statusFilter !== 'all' && { status: statusFilter }),
    ...(searchTerm && { searchTerm }),
  });

  const deleteOrderMutation = useDeleteOrder();

  const getOrderStatusBadge = (status: OrderStatus) => {
    const statusConfig = {
      [OrderStatus.Pending]: { label: t('status.pending'), variant: 'secondary' as const },
      [OrderStatus.Processing]: { label: t('status.processing'), variant: 'default' as const },
      [OrderStatus.Shipped]: { label: t('status.shipped'), variant: 'outline' as const },
      [OrderStatus.Delivered]: { label: t('status.delivered'), variant: 'default' as const },
      [OrderStatus.Cancelled]: { label: t('status.cancelled'), variant: 'destructive' as const },
      [OrderStatus.Refunded]: { label: t('status.refunded'), variant: 'secondary' as const },
    };

    const config = statusConfig[status];
    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };

  const handleDelete = async (id: string, orderNumber: string) => {
    try {
      await deleteOrderMutation.mutateAsync(id);
      toast.success(`Sipariş ${orderNumber} başarıyla silindi`);
    } catch (error) {
      toast.error('Sipariş silinirken bir hata oluştu');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('tr-TR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(dateString));
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Yükleniyor...</div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">
            Siparişler yüklenirken bir hata oluştu
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('list')}</CardTitle>
        <div className="flex gap-4 items-center">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Sipariş no, müşteri adı ile ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select
            value={statusFilter.toString()}
            onValueChange={(value) => setStatusFilter(value === 'all' ? 'all' : parseInt(value) as OrderStatus)}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Durum filtrele" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tüm Durumlar</SelectItem>
              <SelectItem value={OrderStatus.Pending.toString()}>{t('status.pending')}</SelectItem>
              <SelectItem value={OrderStatus.Processing.toString()}>{t('status.processing')}</SelectItem>
              <SelectItem value={OrderStatus.Shipped.toString()}>{t('status.shipped')}</SelectItem>
              <SelectItem value={OrderStatus.Delivered.toString()}>{t('status.delivered')}</SelectItem>
              <SelectItem value={OrderStatus.Cancelled.toString()}>{t('status.cancelled')}</SelectItem>
              <SelectItem value={OrderStatus.Refunded.toString()}>{t('status.refunded')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Sipariş No</TableHead>
              <TableHead>Müşteri</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead>Ürün Sayısı</TableHead>
              <TableHead>Toplam Tutar</TableHead>
              <TableHead>Tarih</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {orders.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  Sipariş bulunamadı
                </TableCell>
              </TableRow>
            ) : (
              orders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-medium">{order.orderNumber}</TableCell>
                  <TableCell>{order.customerName}</TableCell>
                  <TableCell>{getOrderStatusBadge(order.status)}</TableCell>
                  <TableCell>{order.itemCount}</TableCell>
                  <TableCell>{formatCurrency(order.totalAmount-order.couponDiscountAmount-order.usedPoints)}</TableCell>
                  <TableCell>{formatDate(order.createdAt)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/admin/orders/${order.id}`)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {canDelete && (
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Siparişi Sil</AlertDialogTitle>
                              <AlertDialogDescription>
                                {order.orderNumber} numaralı siparişi silmek istediğinizden emin misiniz?
                                Bu işlem geri alınamaz.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>İptal</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDelete(order.id, order.orderNumber)}
                                className="bg-red-600 hover:bg-red-700"
                              >
                                Sil
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
