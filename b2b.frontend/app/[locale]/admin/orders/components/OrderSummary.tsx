import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Order } from '@/types/order';

interface OrderSummaryProps {
  order: Order;
  formatCurrency: (amount: number) => string;
}

export default function OrderSummary({ order, formatCurrency }: OrderSummaryProps) {
  const subtotal = order.totalAmount - order.shippingAmount - order.taxAmount + order.discountAmount;
  return (
    <Card className="dark:bg-gray-800 dark:border-gray-700">
      <CardHeader>
        <CardTitle className="text-lg dark:text-white">Sipar<PERSON>ş Özeti</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Ara toplam:</span>
          <span className="font-medium dark:text-white">{formatCurrency(subtotal)}</span>
        </div>

        {order.discountAmount > 0 && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">İndirim:</span>
            <span className="font-medium text-green-600 dark:text-green-400">-{formatCurrency(order.discountAmount)}</span>
          </div>
        )}

        {order.couponDiscountAmount > 0 && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">Kupon indirimi:</span>
            <span className="font-medium text-green-600 dark:text-green-400">-{formatCurrency(order.couponDiscountAmount)}</span>
          </div>
        )}

        {order.usedPoints > 0 && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">Kullanılan puan indirimi:</span>
            <span className="font-medium text-green-600 dark:text-green-400">-{formatCurrency(order.usedPoints)}</span>
          </div>
        )}

        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Teslimat (Standart teslimat):</span>
          <span className="font-medium dark:text-white">{formatCurrency(order.shippingAmount)}</span>
        </div>

        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Vergi:</span>
          <span className="font-medium dark:text-white">{formatCurrency(order.taxAmount)}</span>
        </div>

        <div className="border-t dark:border-gray-600 pt-3">
          <div className="flex justify-between">
            <span className="font-semibold text-gray-900 dark:text-white">Sipariş toplamı:</span>
            <span className="font-bold text-lg text-gray-900 dark:text-white">{formatCurrency(order.totalAmount - order.couponDiscountAmount - order.usedPoints)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
