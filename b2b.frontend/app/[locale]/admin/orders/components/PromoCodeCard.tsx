interface PromoCodeCardProps {
  promoCode?: string | null;
  discountAmount?: number;
}

export default function PromoCodeCard({ promoCode, discountAmount }: PromoCodeCardProps) {
  // Sadece gerçekten bir promo kodu kullanıldığında göster
  if (!promoCode || !discountAmount || discountAmount <= 0) return null;

  return (
    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border dark:border-gray-700">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Kupon kodu</span>
        <span className="text-sm bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded font-medium">
          {promoCode}
        </span>
      </div>
      {discountAmount > 0 && (
        <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
          İndirim: {new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY',
          }).format(discountAmount)}
        </div>
      )}
    </div>
  );
}
