export enum OrderStatus {
  Pending = 0,
  Processing = 1,
  Shipped = 2,
  Delivered = 3,
  Cancelled = 4,
  Refunded = 5
}

export enum PaymentStatus {
  Pending = 0,
  Processing = 1,
  Completed = 2,
  Failed = 3,
  Cancelled = 4,
  Refunded = 5
}

export enum ShipmentStatus {
  Pending = 0,
  Processing = 1,
  Shipped = 2,
  InTransit = 3,
  Delivered = 4,
  Cancelled = 5,
  Returned = 6
}

export enum AddressType {
  Billing = 0,
  Shipping = 1
}

export interface Order {
  id: string;
  customerId: string;
  addressId?: string;
  orderNumber: string;
  status: OrderStatus;
  totalAmount: number;
  discountAmount: number;
  shippingAmount: number;
  taxAmount: number;
  couponCode?: string
  couponDiscountAmount: number;
  usedPoints: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  customer?: Customer;
  address?: Address;
  orderRows: OrderRow[];
  payments: Payment[];
  shipments: Shipment[];
}

export interface OrderList {
  id: string;
  orderNumber: string;
  status: OrderStatus;
  totalAmount: number;
  couponDiscountAmount: number;
  usedPoints: number;
  createdAt: string;
  customerName: string;
  itemCount: number;
}

export interface OrderCreate {
  customerId: string;
  addressId?: string;
  discountAmount?: number;
  shippingAmount?: number;
  taxAmount?: number;
  notes?: string;
  orderRows: OrderRowCreate[];
}

export interface OrderUpdate {
  id: string;
  status: OrderStatus;
  discountAmount: number;
  shippingAmount: number;
  taxAmount: number;
  notes?: string;
}

export interface OrderStatusUpdate {
  id: string;
  status: OrderStatus;
  notes?: string;
}

export interface OrderRow {
  id: string;
  orderId: string;
  productId: string;
  quantity: number;
  price: number;
  discountedPrice: number;
  product?: Product;
}

export interface OrderRowCreate {
  productId: string;
  quantity: number;
  price: number;
  discountedPrice: number;
}

export interface OrderTimeline {
  orderId: string;
  orderNumber: string;
  events: OrderTimelineEvent[];
}

export interface OrderTimelineEvent {
  id: string;
  eventType: string; // "ORDER_CREATED", "PAYMENT_COMPLETED", "SHIPMENT_CREATED", etc.
  title: string;
  description: string;
  eventDate: string;
  status?: string;
  notes?: string;
  metadata?: Record<string, any>;
}

export interface Payment {
  id: string;
  orderId: string;
  amount: number;
  description: string;
  paymentMethod: string;
  maskedCardNumber: string;
  status: PaymentStatus;
  paymentResponse: string;
  paymentResponseCode: string;
  paymentResponseMessage: string;
  paymentResponseTransactionId: string;
  createdAt: string;
}

export interface Shipment {
  id: string;
  orderId: string;
  trackingNumber: string;
  cargoKey: string;
  carrier: string;
  status: ShipmentStatus;
  shippedAt: string;
  deliveredAt?: string;
  notes?: string;
  createdAt: string;
}

export interface Customer {
  id: string;
  nameSurname: string;
  phoneNumber?: string;
  email?: string;
  taxOrIdentityNumber?: string;
  taxOffice?: string;
}

export interface Address {
  id: string;
  name: string; // Backend'de "Name" alanı var
  line1: string; // Backend'de "Line1" alanı var
  line2?: string; // Backend'de "Line2" alanı var
  city: string;
  district: string;
  country: string; // Backend'de "Country" alanı var
  postalCode?: string;
  addressType: AddressType; // Backend'de "AddressType" alanı var
  isDefault: boolean; // Backend'de "IsDefault" alanı var
  customerId?: string; // Backend'de "CustomerId" alanı var
  dealerId?: string; // Backend'de "DealerId" alanı var
  createdAt: string; // Backend'de "CreatedAt" alanı var
  updatedAt: string; // Backend'de "UpdatedAt" alanı var

  // Computed properties for backward compatibility
  title?: string; // name ile aynı
  addressLine?: string; // line1 + line2 birleşimi
}

export interface Product {
  id: string;
  name: string;
  description?: string;
  sku: string;
  price: number;
  stockQuantity?: number;
  images: ProductImage[];
}

export interface OrderFilterParams {
  page?: number;
  pageSize?: number;
  status?: OrderStatus;
  customerId?: string;
  startDate?: string;
  endDate?: string;
  searchTerm?: string;
}

export interface OrderAnalytics {
  totalSales: number;
  orderCount: number;
}

export interface ShippingCarrier {
  id: string;
  name: string;
  shortCode: string;
  isActive: boolean;
  isImplemented: boolean;
  logoUrl?: string;
  sortOrder: number;
}

export interface CreateShipmentWithCarrierRequest {
  carrierShortCode: string;
  recipientName: string;
  recipientPhone: string;
  recipientEmail?: string;
  address: string;
  city: string;
  district: string;
  postalCode?: string;
  weight?: number;
  declaredValue?: number;
  specialInstructions?: string;
}

export interface ProductImage {
  id: string;
  productId: string;
  originalImagePath: string;
  thumbnailSmallPath: string;
  thumbnailMediumPath: string;
  altText: string;
  sortOrder: number;
  isMainImage: boolean;
}
