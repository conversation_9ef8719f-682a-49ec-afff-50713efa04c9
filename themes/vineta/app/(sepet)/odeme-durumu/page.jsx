import Image from "next/image";
import { serverApi } from "@/lib/api/server";

// Metadata
export const metadata = {
    title: "Siparişinizi Aldık || Future Cosmetics",
    description: "Siparişiniz başarıyla alındı. Sipariş durumunuzu takip edebilirsiniz.",
    robots: "noindex, nofollow", // Ödeme sayfaları indexlenmemeli
};

const fmtPrice = (v) =>
    typeof v === "number"
        ? v.toLocaleString("tr-TR", { style: "currency", currency: "TRY" })
        : v || "-";

// Client-side fiyat hesaplama fonksiyonları
const calculatePricing = {
    discountAmount: (price, discountedPrice, quantity = 1) => {
        if (!discountedPrice || discountedPrice >= price) return 0;
        return (price - discountedPrice) * quantity;
    },
    taxAmount: (grossAmount) => {
        return grossAmount - (grossAmount / 1.2);
    },
    netAmount: (grossAmount) => {
        return grossAmount / 1.2;
    }
};

function SuccessView({
    orderNumber,
    orderDate,
    orderTotal,
    couponDiscountAmount,
    usedPoints,
    paymentMethod,
    shipName,
    shipAddress,
    shipCity,
    shipPostalCode,
    shipPhone,
    shipEmail,
    items = [],
}) {
    return (
        <div className="flat-spacing-29">
            <div className="container">
                {/* Başlık */}
                <div className="flat-spacing pb-0">
                    <div className="title-success-order text-center">
                        <Image className="icon" src="/images/section/success.svg" alt="success" width={96} height={96} />
                        <div className="box-title">
                            <h3 className="title">Siparişiniz Başarıyla Alındı</h3>
                            {orderNumber ? (
                                <p className="text-md text-main">Sipariş numaranız: {orderNumber}</p>
                            ) : null}
                        </div>
                    </div>
                </div>

                <div className="row">
                    {/* Sol Alan */}
                    <div className="col-xl-8">
                        <div className="tf-main-success">
                            {/* Özet Bilgiler */}
                            <div className="box-progress-order">
                                <div className="order-progress-item order-code text-center">
                                    <div className="title text-sm fw-medium">Sipariş No</div>
                                    <div className="text-md fw-medium code">{orderNumber || "-"}</div>
                                </div>
                                <div className="order-progress-item order-date text-center">
                                    <div className="title text-sm fw-medium">Sipariş Tarihi</div>
                                    <div className="text-md fw-medium date">{orderDate || "-"}</div>
                                </div>
                                <div className="order-progress-item order-total text-center">
                                    <div className="title text-sm fw-medium">Toplam</div>
                                    <div className="text-md fw-medium total">{orderTotal || "-"}</div>
                                </div>
                                <div className="order-progress-item payment-method text-center">
                                    <div className="title text-sm fw-medium">Ödeme Yöntemi</div>
                                    <div className="text-md fw-medium metod">{paymentMethod || "-"}</div>
                                </div>
                            </div>

                            {/* Zaman Çizelgesi (statik gösterim) */}
                            <div className="box-timeline-order">
                                <div className="timeline-item active text-center">
                                    <div className="box-icon">
                                        <span className="icon icon-confirm"></span>
                                    </div>
                                    <div className="content">
                                        <div className="title fw-medium text-md">Sipariş Alındı</div>
                                    </div>
                                </div>
                                <div className="timeline-item text-center">
                                    <div className="box-icon">
                                        <span className="icon icon-shipping"></span>
                                    </div>
                                    <div className="content">
                                        <div className="title fw-medium text-md">Kargoya Verildi</div>
                                    </div>
                                </div>
                                <div className="timeline-item text-center">
                                    <div className="box-icon">
                                        <span className="icon icon-location"></span>
                                    </div>
                                    <div className="content">
                                        <div className="title fw-medium text-md">Teslim Edildi</div>
                                    </div>
                                </div>
                            </div>

                            {/* Gönderim Adresi */}
                            <div className="box-ship-address">
                                <div className="row justify-content-between">
                                    <div className="col-12 col-sm-5">
                                        <div className="box-address">
                                            <div className="title text-lg fw-medium">Teslimat Adresi</div>
                                            <ul className="list-info">
                                                <li><span className="text-sm text-main">Ad Soyad: </span><span className="text-sm fw-medium">{shipName || "-"}</span></li>
                                                <li><span className="text-sm text-main">Adres: </span><span className="text-sm fw-medium">{shipAddress || "-"}</span></li>
                                                <li><span className="text-sm text-main">Şehir: </span><span className="text-sm fw-medium">{shipCity || "-"}</span></li>
                                                <li><span className="text-sm text-main">Posta Kodu: </span><span className="text-sm fw-medium">{shipPostalCode || "-"}</span></li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div className="col-12 col-sm-5">
                                        <div className="box-address">
                                            <div className="title text-lg fw-medium">İletişim</div>
                                            <ul className="list-info">
                                                <li><span className="text-sm text-main">Telefon: </span><span className="text-sm fw-medium">{shipPhone || "-"}</span></li>
                                                <li><span className="text-sm text-main">E-posta: </span><span className="text-sm fw-medium">{shipEmail || "-"}</span></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                    {/* Sağ Sidebar - Sipariş Özeti */}
                    <div className="col-xl-4">
                        <div className="tf-page-cart-sidebar sidebar-order-success">
                            <div className="cart-box order-box">
                                <div className="title text-lg fw-medium">Sipariş Özeti</div>
                                <ul className="list-order-product">
                                    {items.map((it, idx) => (
                                        <li className="order-item" key={idx}>
                                            <figure className="img-product">
                                                <Image src={it.imageUrl || "/images/placeholder-product.svg"} alt={it.name || "product"} width={64} height={64} />
                                                <span className="quantity">{it.quantity}</span>
                                            </figure>
                                            <div className="content">
                                                <div className="info">
                                                    <p className="name text-sm fw-medium">{it.name}</p>
                                                    {it.variant && <span className="variant">{it.variant}</span>}
                                                </div>
                                                <div className="price-info">
                                                    {it.hasDiscount && (
                                                        <>
                                                            <span style={{ textDecoration: 'line-through' }} className="text-sm">{fmtPrice(it.originalPrice * it.quantity)}</span> <br />
                                                        </>
                                                    )}
                                                    <span className="price text-sm fw-medium">{fmtPrice(it.finalPrice)}</span>
                                                </div>
                                            </div>
                                        </li>
                                    ))}
                                </ul>

                                {/* Detaylı Sipariş Özeti */}
                                {items.length > 0 && (
                                    <ul className="list-total">
                                        {/* İndirim varsa orijinal tutarı göster */}
                                        {items.some(item => item.hasDiscount) && (
                                            <li className="total-item text-sm d-flex justify-content-between">
                                                <span>Ara Toplam:</span>
                                                <span className="price-sub fw-medium text-decoration-line-through">
                                                    {fmtPrice(items.reduce((sum, item) => sum + item.originalTotalAmount, 0))}
                                                </span>
                                            </li>
                                        )}

                                        {/* İndirim tutarı */}
                                        {items.some(item => item.hasDiscount) && (
                                            <li className="total-item text-sm d-flex justify-content-between">
                                                <span>İndirim:</span>
                                                <span className="price-discount fw-medium text-success">
                                                    -{fmtPrice(items.reduce((sum, item) => sum + item.discountAmount, 0))}
                                                </span>
                                            </li>
                                        )}

                                        {/* Kupon indirimi */}
                                        {couponDiscountAmount > 0 && (
                                            <li className="total-item text-sm d-flex justify-content-between">
                                                <span>Kupon İndirimi:</span>
                                                <span className="price-discount fw-medium text-success">
                                                    -{fmtPrice(couponDiscountAmount)}
                                                </span>
                                            </li>
                                        )}

                                        {/* Puan indirimi */}
                                        {usedPoints > 0 && (
                                            <li className="total-item text-sm d-flex justify-content-between">
                                                <span>Puan İndirimi:</span>
                                                <span className="price-discount fw-medium text-success">
                                                    -{fmtPrice(usedPoints)}
                                                </span>
                                            </li>
                                        )}

                                        <li className="total-item text-sm d-flex justify-content-between">
                                            <span>Net Tutar (Vergi Hariç):</span>
                                            <span className="price-sub fw-medium">
                                                {fmtPrice(items.reduce((sum, item) => sum + item.netAmount, 0))}
                                            </span>
                                        </li>
                                        <li className="total-item text-sm d-flex justify-content-between">
                                            <span>KDV (%20):</span>
                                            <span className="price-tax fw-medium">
                                                {fmtPrice(items.reduce((sum, item) => sum + item.taxAmount, 0))}
                                            </span>
                                        </li>
                                        <li className="total-item text-sm d-flex justify-content-between">
                                            <span>Kargo:</span>
                                            <span className="price-ship fw-medium">Ücretsiz</span>
                                        </li>
                                    </ul>
                                )}

                                <div className="subtotal text-lg fw-medium d-flex justify-content-between">
                                    <span>Toplam:</span>
                                    <span className="total-price-order">{orderTotal || "-"}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

function FailureView({ message }) {
    return (
        <section className="flat-spacing pb-0">
            <div className="container">
                <div className="title-success-order text-center">
                    <Image className="icon" src="/images/section/icon-return.svg" alt="fail" width={96} height={96} />
                    <div className="box-title">
                        <h3 className="title">Siparişiniz Oluşturulamadı</h3>
                        {message ? <p className="text-md text-main">{message}</p> : null}
                    </div>
                </div>
            </div>
        </section>
    );
}

export default async function Page({ searchParams }) {
    // searchParams'ı await et
    const params = await searchParams;

    const getParam = (key) => {
        const v = params?.[key];
        return Array.isArray(v) ? v[0] : v;
    };

    const isSuccess = (getParam("success") || "").toLowerCase() === "true";
    const orderId = getParam("orderId");
    const message = getParam("message");

    if (!isSuccess) {
        return (
            <main>
                <FailureView message={message} />
            </main>
        );
    }

    // Başarılı durumda: orderId ile siparişi server-side çek
    if (!orderId) {
        return (
            <main>
                <FailureView message="Sipariş numarası bulunamadı." />
            </main>
        );
    }

    try {
        const resp = await serverApi.get(`/orders/${orderId}`);

        const order = resp?.data;

        if (!order) {
            return (
                <main>
                    <FailureView message="Sipariş bilgisi alınamadı." />
                </main>
            );
        }

        const orderNumber = order.orderNumber;
        const orderDate = order.createdAt ? new Date(order.createdAt).toLocaleDateString("tr-TR") : "-";
        const orderTotal = fmtPrice(order.totalAmount - order.couponDiscountAmount - order.usedPoints);
        const paymentMethod = (order.payments && order.payments[0]?.paymentMethod) || "-";

        const addr = order.address || {};
        const shipName = addr.name || "-";
        const shipAddress = addr.line1 || "-";
        const shipCity = addr.city || "-";
        const shipPostalCode = addr.postalCode || "-";
        const shipPhone = order.customer?.phoneNumber || "-";
        const shipEmail = order.customer?.email || "-";

        const items = (order.orderRows || []).map((r) => {
            const originalPrice = r.price || 0;
            const discountedPrice = r.discountedPrice || null;
            const quantity = r.quantity || 1;

            // Etkili fiyat (indirimli varsa onu, yoksa orijinal)
            const effectivePrice = discountedPrice || originalPrice;
            const finalPrice = effectivePrice * quantity;

            // İndirim hesaplama
            const discountAmount = calculatePricing.discountAmount(originalPrice, discountedPrice, quantity);
            const hasDiscount = discountAmount > 0;

            // Vergi ve net tutar hesaplama
            const taxAmount = calculatePricing.taxAmount(finalPrice);
            const netAmount = calculatePricing.netAmount(finalPrice);

            return {
                name: r.product?.name || "Ürün",
                variant: r.product?.variant || "",
                quantity: quantity,
                originalPrice: originalPrice,
                discountedPrice: effectivePrice,
                finalPrice: finalPrice,
                originalTotalAmount: originalPrice * quantity,
                discountAmount: discountAmount,
                taxAmount: taxAmount,
                netAmount: netAmount,
                hasDiscount: hasDiscount,
                imageUrl: r.product?.images?.[0]?.thumbnailMediumPath || null,
            };
        });

        return (
            <main>
                <SuccessView
                    orderNumber={orderNumber}
                    orderDate={orderDate}
                    orderTotal={orderTotal}
                    couponDiscountAmount={order.couponDiscountAmount}
                    usedPoints={order.usedPoints}
                    paymentMethod={paymentMethod}
                    shipName={shipName}
                    shipAddress={shipAddress}
                    shipCity={shipCity}
                    shipPostalCode={shipPostalCode}
                    shipPhone={shipPhone}
                    shipEmail={shipEmail}
                    items={items}
                />
            </main>
        );
    } catch (e) {
        return (
            <main>
                <FailureView message={e?.message || "Sipariş verisi alınırken bir hata oluştu."} />
            </main>
        );
    }
}

