"use client";

import { useState, useEffect } from 'react';
import { formatTLPrice } from '@/utils/currency';
import { useCustomerCoupons } from '@/hooks/useCustomerCoupons';

export default function CouponUsage({
  orderSummary,
  onCouponChange
}) {
  const { coupons, loading, error, validateCoupon } = useCustomerCoupons();
  const [selectedCoupon, setSelectedCoupon] = useState(null);
  const [couponCode, setCouponCode] = useState('');
  const [validationError, setValidationError] = useState('');
  const [isValidating, setIsValidating] = useState(false);

  // Kupon seçimi değiştiğinde parent component'e bildir
  useEffect(() => {
    onCouponChange(selectedCoupon);
  }, [selectedCoupon, onCouponChange]);

  // Kupon seçme
  const handleSelectCoupon = async (coupon) => {
    setIsValidating(true);
    setValidationError('');

    try {
      const validation = await validateCoupon(coupon.couponCode, orderSummary.totalAmount);

      if (validation.isValid) {
        setSelectedCoupon({
          ...coupon,
          discountAmount: validation.discountAmount
        });
        setCouponCode(coupon.couponCode);
      } else {
        setValidationError(validation.message || 'Kupon kullanılamıyor');
      }
    } catch (err) {
      setValidationError(err.message);
    } finally {
      setIsValidating(false);
    }
  };

  // Kupon kaldırma
  const handleRemoveCoupon = () => {
    setSelectedCoupon(null);
    setCouponCode('');
    setValidationError('');
  };

  // Kupon kodu ile manuel doğrulama
  const handleManualValidation = async () => {
    if (!couponCode.trim()) return;

    setIsValidating(true);
    setValidationError('');

    try {
      const validation = await validateCoupon(couponCode, orderSummary.totalAmount);

      if (validation.isValid) {
        setSelectedCoupon({
          couponCode: couponCode,
          name: validation.coupon?.name || couponCode,
          discountAmount: validation.discountAmount,
          discountType: validation.discountType
        });
      } else {
        setValidationError(validation.message || 'Kupon kullanılamıyor');
      }
    } catch (err) {
      setValidationError(err.message);
    } finally {
      setIsValidating(false);
    }
  };

  if (loading) {
    return (
      <div className="cart-box mb-3">
        <div className="title text-lg fw-medium">Kupon Kullanımı</div>
        <div className="text-center p-3">
          <div className="spinner-border spinner-border-sm" role="status">
            <span className="visually-hidden">Yükleniyor...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="cart-box mb-3">
      <div className="title text-lg fw-medium">Kupon Kullanımı</div>

      <div className="mt-3">
        {/* Manuel kupon kodu girişi */}
        <div className="mb-3">
          <label className="form-label text-sm">Kupon Kodu:</label>
          <div className="d-flex gap-2">
            <input
              type="text"
              className="form-control"
              value={couponCode}
              onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
              placeholder="Kupon kodunu girin"
              disabled={isValidating}
            />
            <button
              type="button"
              className="tf-btn btn-blue-2 animate-btn"
              onClick={handleManualValidation}
              disabled={!couponCode.trim() || isValidating}
            >
              {isValidating ? 'Kontrol Ediliyor...' : 'Uygula'}
            </button>
          </div>
        </div>

        {/* Hata mesajı */}
        {validationError && (
          <div className="alert alert-danger p-2 mb-3">
            <small>{validationError}</small>
          </div>
        )}

        {/* Seçili kupon bilgisi */}
        {selectedCoupon && (
          <div className="selected-coupon p-3 bg-success bg-opacity-10 rounded mb-3">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <strong className="text-success">{selectedCoupon.name || selectedCoupon.couponCode}</strong>
                <div className="text-sm text-muted">
                  {formatTLPrice(selectedCoupon.discountAmount)} indirim
                </div>
              </div>
              <button
                type="button"
                className="btn btn-sm btn-outline-danger"
                onClick={handleRemoveCoupon}
              >
                Kaldır
              </button>
            </div>
          </div>
        )}

        {/* Mevcut kuponlar listesi */}
        {coupons.length > 0 && !selectedCoupon && (
          <div className="available-coupons">
            <h6 className="text-sm fw-medium mb-2">Kullanılabilir Kuponlarınız:</h6>
            <div className="coupons-list">
              {coupons.map((coupon) => (
                <CouponCard
                  key={coupon.id}
                  coupon={coupon}
                  onSelect={() => handleSelectCoupon(coupon)}
                  disabled={isValidating}
                />
              ))}
            </div>
          </div>
        )}

        {coupons.length === 0 && !error && (
          <p className="text-sm text-muted">
            Kullanılabilir kuponunuz bulunmamaktadır.
          </p>
        )}
      </div>
    </div>
  );
}

// Kupon kartı komponenti
function CouponCard({ coupon, onSelect, disabled }) {
  const getDiscountText = () => {
    console.log(coupon);
    if (coupon.discountTypeText === "Yüzde") {
      return `%${coupon.discountAmount} İndirim`;
    } else {
      return `${formatTLPrice(coupon.discountAmount)} İndirim`;
    }
  };

  const formatExpirationDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <div className="coupon-card border rounded-3 p-3 mb-3 position-relative bg-white shadow-sm" style={{ borderLeft: '4px solid #28a745' }}>
      <div className="d-flex align-items-center">
        {/* Kupon ikonu */}
        <div className="coupon-icon me-3">
          <div className="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style={{ width: '50px', height: '50px' }}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M22 10V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v4c1.1 0 2 .9 2 2s-.9 2-2 2v4a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-4c-1.1 0-2-.9-2-2s.9-2 2-2z" />
            </svg>
          </div>
        </div>

        {/* Kupon bilgileri */}
        <div className="flex-grow-1">
          <div className="coupon-title fw-bold text-dark mb-1">
            {coupon.name || coupon.couponCode}
          </div>
          <div className="coupon-discount text-success fw-bold fs-6 mb-1">
            {getDiscountText()}
          </div>
          <div className="coupon-details">
            <div className="text-sm text-muted mb-1">
              Min. sepet tutarı {formatTLPrice(coupon.minimumCartAmount || 0)}
            </div>
            <div className="text-sm text-muted">
              {formatExpirationDate(coupon.expirationDate)} tarihine kadar kullanılabilir
            </div>
          </div>
        </div>

        {/* Kullan butonu */}
        <div className="coupon-action">
          <button
            type="button"
            className="tf-btn btn-success text-white px-4 py-2"
            onClick={onSelect}
            disabled={disabled}
            style={{ borderRadius: '25px' }}
          >
            Kullan
          </button>
        </div>
      </div>
    </div>
  );
}
