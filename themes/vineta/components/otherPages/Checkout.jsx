"use client";

import { useContextElement } from "@/context/Context";
import Link from "next/link";
import { formatTLPrice } from "@/utils/currency";
import Image from "next/image";
import { useState, useEffect } from "react";
import { useCustomerAuth } from "@/hooks/useCustomerAuth";
import CheckoutAddressSelector from "@/components/checkout/CheckoutAddressSelector";
import PointUsage from "@/components/checkout/PointUsage";
import CouponUsage from "@/components/checkout/CouponUsage";
import { initializeIyzicoPayment } from "@/hooks/usePayment";
export default function Checkout() {
  const {
    cartProducts,
    totalPrice,
  } = useContextElement();

  const { session, isAuthenticated } = useCustomerAuth();
  const [shippingAddress, setShippingAddress] = useState(null);
  const [billingAddress, setBillingAddress] = useState(null);
  const [addressRefreshKey, setAddressRefreshKey] = useState(0);
  const [usedPoints, setUsedPoints] = useState(0);
  const [selectedCoupon, setSelectedCoupon] = useState(null);

  // Adres oluşturulduğunda diğer adres listelerini yenile
  const handleAddressCreated = () => {
    setAddressRefreshKey(prev => prev + 1);
  };
  const [orderSummary, setOrderSummary] = useState({
    totalOriginalAmount: 0,
    totalDiscountAmount: 0,
    totalTaxAmount: 0,
    totalNetAmount: 0,
    totalAmount: 0,
    shippingAmount: 0,
    finalAmount: 0,
    hasAnyDiscount: false
  });
  const [shippingInfo, setShippingInfo] = useState({
    firstName: "",
    lastName: "",
    address: "",
    city: "",
    zipCode: "",
    phone: "",
    email: "",
    identityNumber: "",
  });

  // Seçili adres değiştiğinde kargo bilgilerini güncelle
  useEffect(() => {
    if (shippingAddress) {
      setShippingInfo({
        firstName: shippingAddress.name.split(' ')[0] || "",
        lastName: shippingAddress.name.split(' ').slice(1).join(' ') || "",
        address: `${shippingAddress.line1} ${shippingAddress.line2}`.trim(),
        city: shippingAddress.city,
        zipCode: shippingAddress.postalCode || "",
        phone: session?.customer?.phoneNumber || "",
        email: session?.customer?.email || "",
      });
    }
  }, [shippingAddress, session]);
  // Sipariş özeti hesaplama
  useEffect(() => {
    if (cartProducts.length > 0) {
      let totalOriginal = 0;
      let totalDiscount = 0;
      let totalTax = 0;
      let totalNet = 0;
      let totalFinal = 0;
      let hasDiscount = false;

      cartProducts.forEach(product => {
        const price = product.oldPrice || 0;
        const discountedPrice = product.price || null;
        const quantity = product.quantity || 1;

        // Orijinal tutar
        totalOriginal += price * quantity;

        // İndirim tutarı - inline hesaplama
        const discountAmount = (discountedPrice && discountedPrice < price)
          ? (price - discountedPrice) * quantity
          : 0;
        totalDiscount += discountAmount;
        if (discountAmount > 0) hasDiscount = true;

        // Etkili fiyat (indirimli veya normal)
        const effectivePrice = discountedPrice || price;
        const itemTotal = effectivePrice * quantity;
        totalFinal += itemTotal;

        // Vergi tutarı - inline hesaplama (KDV %20)
        totalTax += itemTotal - (itemTotal / 1.2);

        // Net tutar (vergi hariç) - inline hesaplama
        totalNet += itemTotal / 1.2;
      });

      // Kargo ücreti (şimdilik 0, gerçek kargo hesaplaması yapılabilir)
      const shippingAmount = 0;

      // Puan indirimi hesapla
      const pointDiscount = usedPoints;

      // Kupon indirimi hesapla
      const couponDiscount = selectedCoupon ? selectedCoupon.discountAmount : 0;

      const finalAmountWithDiscounts = Math.max(0, totalFinal + shippingAmount - pointDiscount - couponDiscount);

      setOrderSummary({
        totalOriginalAmount: totalOriginal,
        totalDiscountAmount: totalDiscount,
        totalTaxAmount: totalTax,
        totalNetAmount: totalNet,
        totalAmount: totalFinal,
        shippingAmount: shippingAmount,
        pointDiscount: pointDiscount,
        couponDiscount: couponDiscount,
        finalAmount: finalAmountWithDiscounts,
        hasAnyDiscount: hasDiscount || pointDiscount > 0 || couponDiscount > 0
      });
    } else {
      setOrderSummary({
        totalOriginalAmount: 0,
        totalDiscountAmount: 0,
        totalTaxAmount: 0,
        totalNetAmount: 0,
        totalAmount: 0,
        shippingAmount: 0,
        finalAmount: 0,
        hasAnyDiscount: false
      });
    }
  }, [cartProducts, usedPoints, selectedCoupon]);
  // Bu useEffect artık gerekli değil, orderSummary kullanıyoruz
  // useEffect(() => {
  //   setFullPrice(cartProducts.reduce((total, product) => total + (product.oldPrice ?? product.price), 0));
  // }, [cartProducts]);
  // Puan kullanımı değişikliği
  const handlePointsChange = (points) => {
    setUsedPoints(points);
  };

  // Kupon kullanımı değişikliği
  const handleCouponChange = (coupon) => {
    setSelectedCoupon(coupon);
  };

  // Form input değişikliklerini yönet
  const handleShippingInfoChange = (e) => {
    const { name, value } = e.target;
    setShippingInfo(prev => ({
      ...prev,
      [name]: value
    }));
  };
  const handleIdentityNumberChange = (e) => {
    const { name, value } = e.target;
    setShippingInfo(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Giriş/Kayıt modalını aç
  const promptAuth = async () => {
    try {
      const loginModal = document.getElementById('login');
      if (loginModal) {
        const bootstrap = await import('bootstrap');
        const instance = new bootstrap.Offcanvas(loginModal);
        instance.show();
        return;
      }
    } catch { }
    alert("Devam etmek için lütfen giriş yapın veya kayıt olun.");
  };

  // Ödeme işlemini başlat
  const handlePayment = async () => {
    if (!isAuthenticated) {
      await promptAuth();
      return;
    }
    if (!cartProducts.length) {
      alert("Sepetiniz boş!");
      return;
    }

    // Gerekli alanları kontrol et
    if (!shippingInfo.firstName || !shippingInfo.lastName || !shippingInfo.address || !shippingInfo.city || !shippingInfo.phone || !shippingInfo.email) {
      alert("Lütfen tüm kargo bilgilerini doldurun!");
      return;
    }

    var productQuantity = 0;
    cartProducts.forEach(product => {
      productQuantity += product.quantity;
    });
    // Puan indiriminin çalışması için indirimin sepetteki ürünlere uygulanması gerekir.
    if (usedPoints > 0 || selectedCoupon?.discountAmount > 0) {
      cartProducts.forEach(product => {
        product.price = product.price - ((usedPoints + selectedCoupon?.discountAmount || 0) / productQuantity);
      });
    }
    const paymentData = {
      shippingAddress,
      billingAddress,
      shippingInfo,
      cartProducts,
      totalPrice: orderSummary.finalAmount,
      usedPoints: usedPoints,
      couponCode: selectedCoupon?.couponCode || null,
      customerId: session?.customer?.id,
    };

    try {
      const response = await initializeIyzicoPayment(paymentData);
      if (response?.isSuccess) {
        // response.raw.paymentPageUrl adresine yönlendirme yap
        window.location.href = response.raw.paymentPageUrl;
      }
    } catch (error) {
      console.error("Payment error:", error);
      alert("Ödeme işlemi sırasında bir hata oluştu!");
    }
  };

  return (
    <div className="flat-spacing-12">
      <div className="container checkout-container">
        <div className="row">
          <div className="col-xl-8 xs-order-2" style={{ order: 1 }}>
            <div className="tf-checkout-cart-main d-flex flex-column">
              {/* Adres Seçimi - Sadece giriş yapılmışsa göster */}
              {isAuthenticated && (
                <div className="checkout-section" style={{ order: 4 }}>
                  <div className="row mb-4">
                    <div className="col-md-6">
                      <CheckoutAddressSelector
                        key={`shipping-${addressRefreshKey}`}
                        customerId={session?.customer?.id}
                        selectedAddress={shippingAddress}
                        onAddressSelect={setShippingAddress}
                        addressType={1}
                        title="Teslimat Adresi"
                        onAddressCreated={handleAddressCreated}
                      />
                    </div>
                    <div className="col-md-6">
                      <CheckoutAddressSelector
                        key={`billing-${addressRefreshKey}`}
                        customerId={session?.customer?.id}
                        selectedAddress={billingAddress}
                        onAddressSelect={setBillingAddress}
                        addressType={0}
                        title="Fatura Adresi"
                        onAddressCreated={handleAddressCreated}
                      />
                    </div>
                  </div>
                </div>
              )}

              <div className="checkout-section box-ip-checkout" style={{ order: 4 }}>
                <div className="title text-xl fw-medium">Kargo Bilgileriniz</div>
                <div className="grid-2 mb_16">
                  <div className="tf-field style-2 style-3">
                    <input
                      className="tf-field-input tf-input"
                      id="firstname"
                      placeholder=" "
                      type="text"
                      name="firstName"
                      value={shippingInfo.firstName}
                      onChange={handleShippingInfoChange}
                    />
                    <label className="tf-field-label" htmlFor="firstname">
                      Adınız
                    </label>
                  </div>
                  <div className="tf-field style-2 style-3">
                    <input
                      className="tf-field-input tf-input"
                      id="lastname"
                      placeholder=" "
                      type="text"
                      name="lastName"
                      value={shippingInfo.lastName}
                      onChange={handleShippingInfoChange}
                    />
                    <label className="tf-field-label" htmlFor="lastname">
                      Soyadınız
                    </label>
                  </div>
                </div>
                {/* <fieldset className="tf-field style-2 style-3 mb_16"> */}
                {/*   <input */}
                {/*     className="tf-field-input tf-input" */}
                {/*     id="country" */}
                {/*     type="text" */}
                {/*     name="country" */}
                {/*     placeholder="" */}
                {/*   /> */}
                {/*   <label className="tf-field-label" htmlFor="country"> */}
                {/*     Country */}
                {/*   </label> */}
                {/* </fieldset> */}
                <fieldset className="tf-field style-2 style-3 mb_16">
                  <input
                    className="tf-field-input tf-input"
                    id="address"
                    type="text"
                    name="address"
                    placeholder=""
                    value={shippingInfo.address}
                    onChange={handleShippingInfoChange}
                  />
                  <label className="tf-field-label" htmlFor="address">
                    Adresiniz
                  </label>
                </fieldset>
                {/* <fieldset className="mb_16"> */}
                {/*   <input */}
                {/*     type="text" */}
                {/*     className="style-2" */}
                {/*     name="apartment" */}
                {/*     placeholder="Apartman Numarası" */}
                {/*   /> */}
                {/* </fieldset> */}
                <div className="grid-3 mb_16">
                  <fieldset className="tf-field style-2 style-3">
                    <input
                      className="tf-field-input tf-input"
                      id="city"
                      type="text"
                      name="city"
                      placeholder=""
                      value={shippingInfo.city}
                      onChange={handleShippingInfoChange}
                    />
                    <label className="tf-field-label" htmlFor="city">
                      Şehir
                    </label>
                  </fieldset>
                  <fieldset className="tf-field style-2 style-3">
                    <input
                      className="tf-field-input tf-input"
                      id="code"
                      type="text"
                      name="zipCode"
                      placeholder=""
                      value={shippingInfo.zipCode}
                      onChange={handleShippingInfoChange}
                    />
                    <label className="tf-field-label" htmlFor="code">
                      Posta Kodu
                    </label>
                  </fieldset>
                  <fieldset className="tf-field style-2 style-3 mb_16">
                    <input
                      className="tf-field-input tf-input"
                      id="identityNumber"
                      type="text"
                      name="identityNumber"
                      placeholder=""
                      value={shippingInfo.identityNumber}
                      onChange={handleIdentityNumberChange}
                      maxLength={11}
                    />
                    <label className="tf-field-label" htmlFor="identityNumber">
                      Kimlik Numaranız
                    </label>
                  </fieldset>
                </div>
                <fieldset className="tf-field style-2 style-3 mb_16">
                  <input
                    className="tf-field-input tf-input"
                    id="phone"
                    type="text"
                    name="phone"
                    placeholder=""
                    value={shippingInfo.phone}
                    onChange={handleShippingInfoChange}
                  />
                  <label className="tf-field-label" htmlFor="phone">
                    Telefon Numaranız (+905998887766)
                  </label>
                </fieldset>
                <fieldset className="tf-field style-2 style-3 mb_16">
                  <input
                    className="tf-field-input tf-input"
                    id="email"
                    type="text"
                    name="email"
                    placeholder=""
                    value={shippingInfo.email}
                    onChange={handleShippingInfoChange}
                  />
                  <label className="tf-field-label" htmlFor="email">
                    E-posta Adresiniz
                  </label>
                </fieldset>
                {/* Ödeme Adımına Geç Butonu */}
                <div className="btn-payment-step mt-4">
                  <button
                    type="button"
                    className="tf-btn btn-dark2 animate-btn w-100"
                    onClick={handlePayment}
                  >
                    Ödeme Adımına Geç
                  </button>
                </div>
                <div className="">
                  <p className="text-sm mt-3 text-red">
                    Ödeme adımına geç butonuna basıldığında Iyzico ödeme formuna yönlendirileceksiniz.
                  </p>
                </div>
              </div>
              {/* İyzico Checkout Form */}
              <div className="box-ip-payment">
                <div className="title">
                  <div className="text-lg fw-medium mb_4">Ödeme</div>
                  <p className="text-sm text-main">
                    Ödemeler İyzico ile güvenli ve şifrelenmiştir."
                  </p>
                </div>

                {/* İyzico Checkout Form Container */}
                <div id="iyzipay-checkout-form" className="responsive"></div>

                <p className="text-dark-6 text-sm mt-3">
                  Kişisel verileriniz siparişinizi işlemek, bu web sitesindeki deneyiminizi desteklemek ve{" "}
                  <Link
                    href={`/gizlilik-politikasi`}
                    className="fw-medium text-decoration-underline link text-sm"
                  >
                    gizlilik politikamızda{" "}
                  </Link>
                  {" "}açıklanan diğer amaçlar için kullanılacaktır.
                </p>
              </div>
            </div>
          </div>
          <div className="col-xl-4 xs-order-1" style={{ order: 2 }}>
            <div className="tf-page-cart-sidebar d-flex flex-column">
              <div className="checkout-component order-1">
                <PointUsage
                  orderSummary={orderSummary}
                  onPointsChange={handlePointsChange}
                  maxUsablePercentage={50}
                />
              </div>

              <div className="checkout-component order-2">
                <CouponUsage
                  orderSummary={orderSummary}
                  onCouponChange={handleCouponChange}
                />
              </div>

              <div className="checkout-component cart-box order-box order-3">
                <div className="title text-lg fw-medium">Sepetiniz</div>
                {cartProducts.length ? (
                  <ul className="list-order-product">
                    {cartProducts.map((product, i) => (
                      <li key={i} className="order-item">
                        <figure className="img-product">
                          <Image
                            alt="product"
                            src={product.image}
                            width={144}
                            height={188}
                          />
                          <span className="quantity">{product.quantity}</span>
                        </figure>
                        <div className="content">
                          <div className="info">
                            <p className="name text-sm fw-medium">
                              {product.name}
                            </p>
                          </div>

                          <span className="price text-md fw-medium">
                            {(product.oldPrice && product.oldPrice > product.price) && (
                              <>
                                <span style={{ textDecoration: 'line-through' }} className="text-sm">{formatTLPrice(product.oldPrice)}</span> <br />
                              </>
                            )}
                            {formatTLPrice(product.price * product.quantity)}
                          </span>
                        </div>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="p-4">
                    Sepetiniz Boş! Alışverişe başlamak için ürünlere göz atın.{" "}
                    <Link
                      className="tf-btn btn-dark2 animate-btn mt-3"
                      href="/urunler"
                    >
                      Ürünleri Keşfet
                    </Link>
                  </div>
                )}
                <ul className="list-total">
                  {/* İndirim varsa orijinal tutarı göster */}
                  {orderSummary.hasAnyDiscount && (
                    <li className="total-item text-sm d-flex justify-content-between">
                      <span>Ara Toplam:</span>
                      <span className="price-sub fw-medium text-decoration-line-through">
                        {formatTLPrice(orderSummary.totalOriginalAmount)}
                      </span>
                    </li>
                  )}

                  {/* İndirim tutarı */}
                  {orderSummary.totalDiscountAmount > 0 && (
                    <li className="total-item text-sm d-flex justify-content-between">
                      <span>İndirim:</span>
                      <span className="price-discount fw-medium text-success">
                        -{formatTLPrice(orderSummary.totalDiscountAmount)}
                      </span>
                    </li>
                  )}

                  {/* Puan indirimi */}
                  {orderSummary.pointDiscount > 0 && (
                    <li className="total-item text-sm d-flex justify-content-between">
                      <span>Puan İndirimi:</span>
                      <span className="price-discount fw-medium text-success">
                        -{formatTLPrice(orderSummary.pointDiscount)}
                      </span>
                    </li>
                  )}

                  {/* Kupon indirimi */}
                  {orderSummary.couponDiscount > 0 && (
                    <li className="total-item text-sm d-flex justify-content-between">
                      <span>Kupon İndirimi:</span>
                      <span className="price-discount fw-medium text-success">
                        -{formatTLPrice(orderSummary.couponDiscount)}
                      </span>
                    </li>
                  )}

                  <li className="total-item text-sm d-flex justify-content-between">
                    <span>Net Tutar (Vergi Hariç):</span>
                    <span className="price-sub fw-medium">
                      {formatTLPrice(orderSummary.totalNetAmount)}
                    </span>
                  </li>
                  <li className="total-item text-sm d-flex justify-content-between">
                    <span>KDV (%20):</span>
                    <span className="price-tax fw-medium">
                      {formatTLPrice(orderSummary.totalTaxAmount)}
                    </span>
                  </li>
                  <li className="total-item text-sm d-flex justify-content-between">
                    <span>Kargo:</span>
                    <span className="price-ship fw-medium">
                      {orderSummary.shippingAmount > 0 ? formatTLPrice(orderSummary.shippingAmount) : 'Ücretsiz'}
                    </span>
                  </li>
                </ul>
                <div className="subtotal text-lg fw-medium d-flex justify-content-between">
                  <span>Toplam:</span>
                  <span className="total-price-order">
                    {formatTLPrice(orderSummary.finalAmount)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
